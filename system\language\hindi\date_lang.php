<?php
/**
* System messages translation for CodeIgniter(tm)
*
* <AUTHOR> community
* @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
* @copyright	Pieter <PERSON>rul
* @license	http://opensource.org/licenses/MIT MIT License
* @link	https://codeigniter.com
*/
defined('BASEPATH') OR exit('No direct script access allowed');
$lang['date_year'] = 'साल';
$lang['date_years'] = 'वर्षों';
$lang['date_month'] = 'महीना';
$lang['date_months'] = 'महीने';
$lang['date_week'] = 'सप्ताह';
$lang['date_weeks'] = 'सप्ताह';
$lang['date_day'] = 'दिन';
$lang['date_days'] = 'दिनों';
$lang['date_hour'] = 'घंटा';
$lang['date_hours'] = 'घंटे';
$lang['date_minute'] = 'मिनट';
$lang['date_minutes'] = 'मिनटों';
$lang['date_second'] = 'सेकंड';
$lang['date_seconds'] = 'सेकंड';
$lang['UM12'] = '(यूटीसी -12: 00) बेकर / हौलेंड द्वीप';
$lang['UM11'] = '(यूटीसी -11: 00) नियू';
$lang['UM10'] = '(यूटीसी -10: 00) हवाई-अलूशन मानक समय, कुक आइलैंड्स, ताहिती';
$lang['UM95'] = '(यूटीसी -9: 30) मार्केसस द्वीप समूह';
$lang['UM9'] = '(यूटीसी -9: 00) अलास्का मानक समय, गैंबियर द्वीप समूहप समूह';
$lang['UM8'] = '(यूटीसी -8: 00) प्रशांत मानक समय, क्लिपर्टन द्वीप';
$lang['UM7'] = '(यूटीसी -7: 00) पर्वतीय मानक समय';
$lang['UM6'] = '(यूटीसी -6: 00) केंद्रीय मानक समय';
$lang['UM5'] = '(यूटीसी -5: 00) पूर्वी मानक समय, पश्चिमी कैरेबियन मानक समय';
$lang['UM45'] = '(यूटीसी -4: 30) वेनेजुएला मानक समय';
$lang['UM4'] = '(यूटीसी -4: 00) अटलांटिक मानक समय, पूर्वी कैरेबियाई मानक समय';
$lang['UM35'] = '(यूटीसी -3: 30) न्यूफाउंडलैंड समय क्षेत्र';
$lang['UM3'] = '(यूटीसी -3: 00), अर्जेंटीना, ब्राजील, फ्रेंच गयाना, उरुग्वे';
$lang['UM2'] = '(यूटीसी -2: 00) दक्षिण जॉर्जिया / दक्षिण सैंडविच आइलैंड्स';
$lang['UM1'] = '(यूटीसी -1: 00) अज़ोरेस, केप वर्डे द्वीप';
$lang['UTC'] = '(यूटीसी) ग्रीनविच मीन टाइम, पश्चिमी यूरोपीय समय';
$lang['UP1'] = '(यूटीसी 1: 00) मध्य यूरोपीय समय, पश्चिम अफ्रीका समय';
$lang['UP2'] = '(यूटीसी 2: 00) मध्य अफ्रीका समय, पूर्वी यूरोपीय समय, कलिनिन्ग्राद समय';
$lang['UP3'] = '(यूटीसी 3: 00) मास्को समय, समय पूर्वी अफ्रीका, अरब समय क्षेत्र';
$lang['UP35'] = '(यूटीसी 3: 30) ईरान मानक समय';
$lang['UP4'] = '(यूटीसी 4: 00) अज़रबैजान मानक समय, समारा समय';
$lang['UP45'] = '(यूटीसी 4: 30) अफगानिस्तान';
$lang['UP5'] = '(यूटीसी 5: 00) पाकिस्तान टाइम जोन, येकातेरिनबर्ग समय';
$lang['UP55'] = '(यूटीसी 5: 30) भारतीय मानक समय श्रीलंका का समय';
$lang['UP575'] = '(यूटीसी 5: 45) नेपाल टाइम';
$lang['UP6'] = '(यूटीसी 6: 00) बांग्लादेश मानक समय, भूटान समय, ओम्स्क समय';
$lang['UP65'] = '(यूटीसी 6: 30) कोकोस द्वीप समूह, म्यांमार';
$lang['UP7'] = '(यूटीसी 7: 00) क्रास्नायार्स्क समय, कंबोडिया, लाओस, थाईलैंड, वियतनाम';
$lang['UP8'] = '(यूटीसी 8: 00) ऑस्ट्रेलियाई पश्चिमी मानक समय, बीजिंग समय, इर्कुत्स्क समय';
$lang['UP875'] = '(यूटीसी 8: 45) केंद्रीय पश्चिमी समय क्षेत्र';
$lang['UP9'] = '(यूटीसी 9: 00) जापान मानक समय, कोरिया मानक समय, याकुत्स्क समय';
$lang['UP95'] = '(यूटीसी 9: 30) ऑस्ट्रेलियाई केन्द्रीय समय क्षेत्र';
$lang['UP10'] = '(यूटीसी 10: 00) ऑस्ट्रेलियाई पूर्वी मानक समय, व्लादिवोस्तोक समय';
$lang['UP105'] = '(यूटीसी 10: 30) प्रभु होवे आइलैंड';
$lang['UP11'] = '(यूटीसी 11: 00) Srednekolymsk समय, सोलोमन द्वीप, वानुअतु';
$lang['UP115'] = '(यूटीसी 11: 30) नॉरफ़ॉक आइलैंड';
$lang['UP12'] = '(यूटीसी 12: 00) फिजी, गिल्बर्ट द्वीप समूह, कमचटका समय, न्यूजीलैंड मानक समय';
$lang['UP1275'] = '(यूटीसी 12: 45) चैथम द्वीप मानक समय';
$lang['UP13'] = '(यूटीसी 13: 00) समोआ समय क्षेत्र, समय फीनिक्स द्वीप, टोंगा';
$lang['UP14'] = '(यूटीसी 14: 00) लाइन द्वीपसमूह';
