<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Gmeet extends Student_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model(array('video_conference_model', 'video_conference_settings_model'));
        $this->load->library('gmeet_lib');
    }

    public function index()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);

        $data['title'] = 'Google Meet Classes';
        $data['upcoming'] = $this->_get_student_gmeet_meetings($student, 'upcoming');
        $data['ongoing'] = $this->_get_student_gmeet_meetings($student, 'ongoing');
        $data['completed'] = $this->_get_student_gmeet_meetings($student, 'completed');

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/gmeet/index', $data);
        $this->load->view('layout/student/footer', $data);
    }

    private function _get_student_gmeet_meetings($student, $status = 'all')
    {
        if (!$student) {
            return array();
        }

        $meetings = $this->video_conference_model->getByClass($student->class_id, $student->section_id);
        $gmeet_meetings = array();

        foreach ($meetings as $meeting) {
            if ($meeting->platform == 'gmeet') {
                $current_time = time();
                $start_time = strtotime($meeting->start_time);
                $end_time = strtotime($meeting->end_time);

                $meeting_status = 'scheduled';
                if ($current_time >= $start_time && $current_time <= $end_time) {
                    $meeting_status = 'ongoing';
                } elseif ($current_time > $end_time) {
                    $meeting_status = 'completed';
                } elseif ($current_time < $start_time) {
                    $meeting_status = 'upcoming';
                }

                if ($status == 'all' || $status == $meeting_status) {
                    $meeting->computed_status = $meeting_status;
                    $gmeet_meetings[] = $meeting;
                }
            }
        }

        return $gmeet_meetings;
    }

    public function join($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        $conference = $this->video_conference_model->get($id);

        if (!$conference || $conference->platform != 'gmeet') {
            show_404();
        }

        // Check if student can join
        if (!$this->_can_join_meeting($conference, $student)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">You are not authorized to join this meeting!</div>');
            redirect('user/gmeet');
        }

        // Check timing
        $current_time = time();
        $start_time = strtotime($conference->start_time);
        $end_time = strtotime($conference->end_time);
        $join_before_time = $start_time - (15 * 60);

        if ($current_time < $join_before_time) {
            $this->session->set_flashdata('msg', '<div class="alert alert-warning">Meeting has not started yet. You can join 15 minutes before the scheduled time.</div>');
            redirect('user/gmeet');
        }

        if ($current_time > $end_time) {
            $this->session->set_flashdata('msg', '<div class="alert alert-info">This meeting has already ended.</div>');
            redirect('user/gmeet');
        }

        // Log participation
        $this->_log_participation($id, $student_id, 'joined');

        // Update meeting status
        if ($conference->status == 'scheduled' && $current_time >= $start_time) {
            $this->video_conference_model->updateStatus($id, 'ongoing');
        }

        // Redirect to Google Meet
        redirect($conference->meeting_url);
    }

    private function _can_join_meeting($conference, $student)
    {
        // Check class/section match
        if ($conference->class_id && $conference->section_id) {
            return ($conference->class_id == $student->class_id && $conference->section_id == $student->section_id);
        }

        // Check explicit invitation
        $participants = $this->video_conference_model->getParticipants($conference->id);
        foreach ($participants as $participant) {
            if ($participant->participant_id == $student->id && $participant->participant_type == 'student') {
                return true;
            }
        }

        // Open meeting
        if (!$conference->class_id && !$conference->section_id) {
            return true;
        }

        return false;
    }

    private function _log_participation($conference_id, $student_id, $action)
    {
        $participant_data = array(
            'participant_type' => 'student',
            'participant_id' => $student_id,
            'status' => $action
        );

        if ($action == 'joined') {
            $participant_data['join_time'] = date('Y-m-d H:i:s');
        } elseif ($action == 'left') {
            $participant_data['leave_time'] = date('Y-m-d H:i:s');
        }

        // Check if participant record exists
        $participants = $this->video_conference_model->getParticipants($conference_id);
        $participant_exists = false;

        foreach ($participants as $participant) {
            if ($participant->participant_id == $student_id && $participant->participant_type == 'student') {
                $participant_exists = true;
                $this->video_conference_model->updateParticipantStatus($conference_id, $student_id, 'student', $participant_data);
                break;
            }
        }

        if (!$participant_exists) {
            $this->video_conference_model->addParticipant($conference_id, $participant_data);
        }
    }

    public function timetable()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);

        $data['title'] = 'Google Meet Timetable';
        
        // Get current week's meetings
        $start_date = date('Y-m-d', strtotime('monday this week'));
        $end_date = date('Y-m-d', strtotime('sunday this week'));
        
        $data['weekly_meetings'] = $this->_get_weekly_meetings($student, $start_date, $end_date);
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/gmeet/timetable', $data);
        $this->load->view('layout/student/footer', $data);
    }

    private function _get_weekly_meetings($student, $start_date, $end_date)
    {
        if (!$student) {
            return array();
        }

        $meetings = $this->video_conference_model->getByClass($student->class_id, $student->section_id);
        $weekly_meetings = array();

        foreach ($meetings as $meeting) {
            if ($meeting->platform == 'gmeet') {
                $meeting_date = date('Y-m-d', strtotime($meeting->start_time));
                if ($meeting_date >= $start_date && $meeting_date <= $end_date) {
                    $day_of_week = date('l', strtotime($meeting->start_time));
                    if (!isset($weekly_meetings[$day_of_week])) {
                        $weekly_meetings[$day_of_week] = array();
                    }
                    $weekly_meetings[$day_of_week][] = $meeting;
                }
            }
        }

        return $weekly_meetings;
    }

    public function meeting($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        $conference = $this->video_conference_model->get($id);

        if (!$conference || $conference->platform != 'gmeet') {
            show_404();
        }

        if (!$this->_can_join_meeting($conference, $student)) {
            show_404();
        }

        $data['title'] = 'Google Meet - ' . $conference->title;
        $data['conference'] = $conference;
        $data['can_join'] = $this->_can_join_now($conference);
        $data['participants'] = $this->video_conference_model->getParticipants($id);

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/gmeet/meeting', $data);
        $this->load->view('layout/student/footer', $data);
    }

    private function _can_join_now($conference)
    {
        $current_time = time();
        $start_time = strtotime($conference->start_time);
        $end_time = strtotime($conference->end_time);
        $join_before_time = $start_time - (15 * 60);

        return ($current_time >= $join_before_time && $current_time <= $end_time && $conference->status != 'cancelled');
    }

    public function class_report()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);

        $data['title'] = 'Google Meet Class Reports';
        $data['meetings'] = $this->_get_student_gmeet_meetings($student, 'completed');

        // Calculate attendance statistics
        $total_meetings = count($data['meetings']);
        $attended_meetings = 0;

        foreach ($data['meetings'] as $meeting) {
            $participants = $this->video_conference_model->getParticipants($meeting->id);
            foreach ($participants as $participant) {
                if ($participant->participant_id == $student_id && $participant->participant_type == 'student' && $participant->status == 'joined') {
                    $attended_meetings++;
                    break;
                }
            }
        }

        $data['total_meetings'] = $total_meetings;
        $data['attended_meetings'] = $attended_meetings;
        $data['attendance_percentage'] = $total_meetings > 0 ? round(($attended_meetings / $total_meetings) * 100, 2) : 0;

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/gmeet/class_report', $data);
        $this->load->view('layout/student/footer', $data);
    }

    public function meeting_report($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        $conference = $this->video_conference_model->get($id);

        if (!$conference || $conference->platform != 'gmeet') {
            show_404();
        }

        if (!$this->_can_join_meeting($conference, $student)) {
            show_404();
        }

        $data['title'] = 'Meeting Report - ' . $conference->title;
        $data['conference'] = $conference;
        $data['participants'] = $this->video_conference_model->getParticipants($id);

        // Check if current student attended
        $data['student_attended'] = false;
        $data['student_join_time'] = null;
        $data['student_leave_time'] = null;

        foreach ($data['participants'] as $participant) {
            if ($participant->participant_id == $student_id && $participant->participant_type == 'student') {
                $data['student_attended'] = ($participant->status == 'joined' || $participant->status == 'left');
                $data['student_join_time'] = $participant->join_time;
                $data['student_leave_time'] = $participant->leave_time;
                break;
            }
        }

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/gmeet/meeting_report', $data);
        $this->load->view('layout/student/footer', $data);
    }

    public function leave($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $this->_log_participation($id, $student_id, 'left');

        $this->session->set_flashdata('msg', '<div class="alert alert-success">You have left the Google Meet session.</div>');
        redirect('user/gmeet');
    }

    public function ajax_get_daily_meetings()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        $date = $this->input->post('date');

        $meetings = array();

        if ($student && $date) {
            $all_meetings = $this->video_conference_model->getByClass($student->class_id, $student->section_id);
            
            foreach ($all_meetings as $meeting) {
                if ($meeting->platform == 'gmeet') {
                    $meeting_date = date('Y-m-d', strtotime($meeting->start_time));
                    if ($meeting_date == $date) {
                        $meetings[] = array(
                            'id' => $meeting->id,
                            'title' => $meeting->title,
                            'start_time' => date('H:i', strtotime($meeting->start_time)),
                            'end_time' => date('H:i', strtotime($meeting->end_time)),
                            'subject_name' => $meeting->subject_name,
                            'host_name' => $meeting->host_name . ' ' . $meeting->host_surname,
                            'status' => $meeting->status,
                            'can_join' => $this->_can_join_now($meeting)
                        );
                    }
                }
            }
        }

        echo json_encode($meetings);
    }
}
