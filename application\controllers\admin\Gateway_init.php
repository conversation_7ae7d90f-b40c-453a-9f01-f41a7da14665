<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Gateway_init extends Admin_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('paymentsetting_model');
    }

    public function index()
    {
        if (!$this->rbac->hasPrivilege('payment_methods', 'can_edit')) {
            access_denied();
        }

        $data['title'] = 'Payment Gateway Initialization';
        $data['gateways'] = $this->get_all_gateways();
        $data['current_gateways'] = $this->paymentsetting_model->get();
        
        $this->load->view('layout/header', $data);
        $this->load->view('admin/gateway_init/index', $data);
        $this->load->view('layout/footer', $data);
    }

    public function initialize_all()
    {
        if (!$this->rbac->hasPrivilege('payment_methods', 'can_edit')) {
            access_denied();
        }

        $gateways = $this->get_all_gateways();
        $initialized = 0;
        $updated = 0;

        foreach ($gateways as $gateway) {
            // Check if gateway already exists
            $existing = $this->db->get_where('payment_settings', array('payment_type' => $gateway))->row();
            
            if (!$existing) {
                // Insert new gateway
                $data = array(
                    'payment_type' => $gateway,
                    'api_username' => '',
                    'api_password' => '',
                    'api_signature' => '',
                    'api_email' => '',
                    'paypal_demo' => 'enabled',
                    'is_active' => 'no'
                );
                $this->db->insert('payment_settings', $data);
                $initialized++;
            } else {
                $updated++;
            }
        }

        $message = "Gateway initialization complete. {$initialized} new gateways added, {$updated} existing gateways found.";
        
        $this->session->set_flashdata('msg', $message);
        redirect('admin/gateway_init');
    }

    public function enable_all()
    {
        if (!$this->rbac->hasPrivilege('payment_methods', 'can_edit')) {
            access_denied();
        }

        // First initialize all gateways
        $this->initialize_all_silent();

        // Then enable all
        $this->db->update('payment_settings', array('is_active' => 'yes'));
        $affected = $this->db->affected_rows();

        $message = "All {$affected} payment gateways have been enabled successfully.";

        $this->session->set_flashdata('msg', $message);
        redirect('admin/paymentsettings');
    }

    public function enable_frontend()
    {
        if (!$this->rbac->hasPrivilege('front_cms_setting', 'can_edit')) {
            access_denied();
        }

        // Enable Frontend CMS
        $this->db->update('front_cms_settings', array('is_active_front_cms' => 1));

        // Enable Main Menu
        $this->db->where('slug', 'main-menu');
        $this->db->update('front_cms_menus', array('is_active' => 'yes'));

        // Enable Home Page
        $this->db->where('slug', 'home');
        $this->db->update('front_cms_pages', array('is_active' => 'yes'));

        $message = "Frontend CMS has been enabled successfully! You can now access the homepage at: " . base_url();

        $this->session->set_flashdata('msg', $message);
        redirect('admin/gateway_init');
    }

    private function initialize_all_silent()
    {
        $gateways = $this->get_all_gateways();
        
        foreach ($gateways as $gateway) {
            $existing = $this->db->get_where('payment_settings', array('payment_type' => $gateway))->row();
            
            if (!$existing) {
                $data = array(
                    'payment_type' => $gateway,
                    'api_username' => '',
                    'api_password' => '',
                    'api_signature' => '',
                    'api_email' => '',
                    'paypal_demo' => 'enabled',
                    'is_active' => 'no'
                );
                $this->db->insert('payment_settings', $data);
            }
        }
    }

    private function get_all_gateways()
    {
        return array(
            'paypal',
            'stripe',
            'payu',
            'ccavenue',
            'instamojo',
            'paystack',
            'razorpay',
            'paytm',
            'midtrans',
            'pesapal',
            'flutterwave',
            'ipayafrica',
            'jazzcash',
            'billplz',
            'sslcommerz',
            'walkingm',
            'mollie',
            'cashfree',
            'payfast',
            'toyyibpay',
            'twocheckout',
            'skrill',
            'payhere',
            'onepay'
        );
    }
}
