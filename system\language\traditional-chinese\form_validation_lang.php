<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = '要求含有 {field} 欄位';
$lang['form_validation_isset'] = '{field} 欄位必須有值';
$lang['form_validation_valid_email'] = '{field} 欄位必須是一個有效的 E-mail 地址';
$lang['form_validation_valid_emails'] = '{field} 欄位必須包含有效的 E-mail地址';
$lang['form_validation_valid_url'] = '{field} 欄位必須是一個有效的 URL';
$lang['form_validation_valid_ip'] = '{field} 欄位必須包含一個有效的 IP 位址';
$lang['form_validation_min_length'] = '{field} 欄位最少需要有 {param} 字元';
$lang['form_validation_max_length'] = '{field} 欄位不能超過 {param} 字元';
$lang['form_validation_exact_length'] = '{field} 欄位必須是 {param} 字元';
$lang['form_validation_alpha'] = '{field} 欄位取值只允許為字母';
$lang['form_validation_alpha_numeric'] = '{field} 欄位取值只允許為字母和數字字元';
$lang['form_validation_alpha_numeric_spaces'] = '{field} 欄位取值只允許為字母、數位和空格';
$lang['form_validation_alpha_dash'] = '{field} 欄位取值只允許為字母、數位、底線和破折號';
$lang['form_validation_numeric'] = '{field} 欄位取值只允許為數字';
$lang['form_validation_is_numeric'] = '{field} 欄位必須只包含數字字元';
$lang['form_validation_integer'] = '{field} 欄位必須是整數';
$lang['form_validation_regex_match'] = '{field} 欄位的格是錯誤';
$lang['form_validation_matches'] = '{field} 欄位與 {param} 欄位不符';
$lang['form_validation_differs'] = '{field} 欄位與 {param} 欄位必須不同';
$lang['form_validation_is_unique'] = '{field} 欄位必須是一個獨一無二的值';
$lang['form_validation_is_natural'] = '{field} 欄位必須是自然數';
$lang['form_validation_is_natural_no_zero'] = '{field} 欄位必須是非 0 的自然數';
$lang['form_validation_decimal'] = '{field} 欄位必須是十進位數字';
$lang['form_validation_less_than'] = '{field} 欄位的值必須小於 {param}';
$lang['form_validation_less_than_equal_to'] = '{field} 欄位的值必須小於等於 {param}';
$lang['form_validation_greater_than'] = '{field} 欄位的值必須大於 {param}';
$lang['form_validation_greater_than_equal_to'] = '{field} 欄位的值必須大於等於 {param}';
$lang['form_validation_error_message_not_set'] = '無法取得 {field} 欄位的錯誤資訊';
$lang['form_validation_in_list'] = '{field} 欄位必须是 {param} 中的一種';
