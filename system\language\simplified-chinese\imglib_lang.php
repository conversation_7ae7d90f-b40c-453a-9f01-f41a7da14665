<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['imglib_source_image_required'] = '必须在设置中指定一个源图像。';
$lang['imglib_gd_required'] = '此特性依赖 GD 图形库。';
$lang['imglib_gd_required_for_props'] = '你的服务器必须支持 GD 图形库以确定图像的属性。';
$lang['imglib_unsupported_imagecreate'] = '你的服务器不支持 GD 函数，无法创建图像文件。';
$lang['imglib_gif_not_supported'] = 'GIF 格式由于版权问题一般不提供，如果可能请使用 JPG 格式或 PNG 格式。';
$lang['imglib_jpg_not_supported'] = 'JPG 格式不被支持。';
$lang['imglib_png_not_supported'] = 'PNG 格式不被支持。';
$lang['imglib_jpg_or_png_required'] = '设置中指定的图像缩放方法只能用于 JPG 格式或 PNG 格式。';
$lang['imglib_copy_error'] = '替换文件时发生错误。你的文件目录必须是可写的。';
$lang['imglib_rotate_unsupported'] = '服务器不支持图像旋转。';
$lang['imglib_libpath_invalid'] = '图形库路径错误。在设置中必须指定正确的路径。';
$lang['imglib_image_process_failed'] = '图像处理错误。 请验证服务器支持指定的处理方法，并且图形库路径正确。';
$lang['imglib_rotation_angle_required'] = '必须指定旋转角度。';
$lang['imglib_invalid_path'] = '图像路径错误。';
$lang['imglib_invalid_image'] = '提供的图像无效。';
$lang['imglib_copy_failed'] = '图像复制错误。';
$lang['imglib_missing_font'] = '无法找到使用的字体。';
$lang['imglib_save_failed'] = '图像无法保存，请确保图像和目录可写。';
