<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = "ユーザファイルがセットされていません。";
$lang['upload_file_exceeds_limit'] = "アップロードされたファイルはPHP設定ファイルで許可されている最大サイズを超過しています。";
$lang['upload_file_exceeds_form_limit'] = "アップロードされたファイルはフォームで許可されている最大サイズを超過しています。";
$lang['upload_file_partial'] = "ファイルの一部しかアップロードされていません。";
$lang['upload_no_temp_directory'] = "一時用フォルダーがありません。";
$lang['upload_unable_to_write_file'] = "ファイルをディスクに書き込めませんでした。";
$lang['upload_stopped_by_extension'] = "アップロードは、拡張子により中止されました。";
$lang['upload_no_file_selected'] = "アップロードするファイルが選択されていません。";
$lang['upload_invalid_filetype'] = "アップロードしようとしたファイルは、許可されていない種類です。";
$lang['upload_invalid_filesize'] = "アップロードしようとしたファイルは、許可されているサイズを超えています。";
$lang['upload_invalid_dimensions'] = "アップロードしようとした画像は、幅または高さが最大サイズを超えています。";
$lang['upload_destination_error'] = "アップロードされたファイルを保存先に移動しようとして問題が発生しました。";
$lang['upload_no_filepath'] = "アップロードパスが間違っています。";
$lang['upload_no_file_types'] = "許可されているファイルタイプが指定されていません。";
$lang['upload_bad_filename'] = "送信されたファイル名と同名のファイルがすでにサーバ上に存在します。";
$lang['upload_not_writable'] = "アップロード先のフォルダに書き込みできません。";
