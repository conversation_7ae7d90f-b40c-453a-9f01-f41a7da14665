<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR> S.Ko<PERSON>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license http://opensource.org/licenses/MIT MIT License
 * @link https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'Bidang {field} dibutuhkan.';
$lang['form_validation_isset'] = 'Bidang {field} harus memiliki nilai.';
$lang['form_validation_valid_email'] = 'Bidang {field} harus berisi alamat email yang sah.';
$lang['form_validation_valid_emails'] = 'Bidang {field} harus berisi semua alamat email yang sah.';
$lang['form_validation_valid_url'] = 'Bidang {field} harus berisi URL yang sah.';
$lang['form_validation_valid_ip'] = 'Bidang {field} harus berisi IP yang sah.';
$lang['form_validation_min_length'] = 'Bidang {field} harus setidaknya {param} panjang karakter.';
$lang['form_validation_max_length'] = 'Bidang {field} tidak dapat melebihi {param} panjang karakter.';
$lang['form_validation_exact_length'] = 'Bidang {field} harus tepat {param} panjang karakter.';
$lang['form_validation_alpha'] = 'Bidang {field} hanya dapat berisi karakter abjad.';
$lang['form_validation_alpha_numeric'] = 'Bidang {field} hanya dapat berisi karakter alpha-numerik.';
$lang['form_validation_alpha_numeric_spaces'] = 'Bidang {field} hanya dapat berisi karakter alpha-numerik dan spasi.';
$lang['form_validation_alpha_dash'] = 'Bidang {field} hanya dapat berisi karakter alpha-numeric, garis bawah, dan tanda hubung.';
$lang['form_validation_numeric'] = 'Bidang {field} harus hanya berisi angka.';
$lang['form_validation_is_numeric'] = 'Bidang {field} harus berisi karakter numerik.';
$lang['form_validation_integer'] = 'Bidang {field} harus berisi integer.';
$lang['form_validation_regex_match'] = 'Bidang {field} tidak dalam format yang benar.';
$lang['form_validation_matches'] = 'Bidang {field} tidak cocok dengan bidang {param}.';
$lang['form_validation_differs'] = 'Bidang {field} harus berbeda dari bidang {param}.';
$lang['form_validation_is_unique'] = 'Bidang {field} harus berisi nilai unik.';
$lang['form_validation_is_natural'] = 'Bidang {field} harus hanya mengandung angka.';
$lang['form_validation_is_natural_no_zero'] = 'Bidang {field} harus hanya berisi angka dan harus lebih besar dari nol.';
$lang['form_validation_decimal'] = 'Bidang {field} harus berisi angka desimal.';
$lang['form_validation_less_than'] = 'Bidang {field} harus berisi angka kurang dari {param}.';
$lang['form_validation_less_than_equal_to'] = 'Bidang {field} harus berisi angka kurang dari atau sama dengan {param}.';
$lang['form_validation_greater_than'] = 'Bidang {field} harus berisi angka lebih besar dari {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Bidang {field} harus berisi angka yang lebih besar dari atau sama dengan {param}.';
$lang['form_validation_error_message_not_set'] = 'Tidak dapat mengakses pesan kesalahan sesuai dengan nama bidang Anda {field}.';
$lang['form_validation_in_list'] = 'Bidang {field} harus menjadi salah satu dari: {param}.';
