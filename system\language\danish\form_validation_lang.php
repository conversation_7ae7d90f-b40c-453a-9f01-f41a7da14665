<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= '{field} feltet er påkrævet.';
$lang['form_validation_isset']			= '{field} feltet skal have en værdi.';
$lang['form_validation_valid_email']		= '{field} feltet skal have en gyldig email adresse.';
$lang['form_validation_valid_emails']		= '{field} feltet skal have kun gyldige email adresser.';
$lang['form_validation_valid_url']		= '{field} feltet skal have en gyldig URL internetadresse.';
$lang['form_validation_valid_ip']		= '{field} feltet skal have en gyldig IP adresse.';
$lang['form_validation_min_length']		= '{field} feltet skal være på mindst {param} tegn.';
$lang['form_validation_max_length']		= '{field} feltet skal være på max. {param} tegn.';
$lang['form_validation_exact_length']		= '{field} feltet skal være på nøjagtig {param} tegn.';
$lang['form_validation_alpha']			= '{field} feltet må kun indeholde alfabetiske tegn.';
$lang['form_validation_alpha_numeric']		= '{field} feltet må kun indeholde alfanumeriske tegn.';
$lang['form_validation_alpha_numeric_spaces']	= '{field} feltet må kun indeholde alfanumeriske tegn og mellemrum.';
$lang['form_validation_alpha_dash']		= '{field} feltet må kun indeholde alfanumeriske tegn, bindestreger og understregningstegn.';
$lang['form_validation_numeric']		= '{field} feltet må kun indeholde tal.';
$lang['form_validation_is_numeric']		= '{field} feltet må kun indeholde numeriske tegn.';
$lang['form_validation_integer']		= '{field} feltet skal indeholde et heltal.';
$lang['form_validation_regex_match']		= '{field} feltet er ikke i det rigtige format.';
$lang['form_validation_matches']		= '{field} feltet passer ikke til {param} feltet.';
$lang['form_validation_differs']		= '{field} feltet skal være forskellig fra {param} feltet.';
$lang['form_validation_is_unique'] 		= '{field} feltet skal indeholde en unik værdi.';
$lang['form_validation_is_natural']		= '{field} feltet må kun indeholde tal.';
$lang['form_validation_is_natural_no_zero']	= '{field} feltet må kun indeholde tal og skal være større end nul.';
$lang['form_validation_decimal']		= '{field} feltet skal indeholde et decimaltal.';
$lang['form_validation_less_than']		= '{field} feltet skal indeholde et tal mindre end {param}.';
$lang['form_validation_less_than_equal_to']	= '{field} feltet skal indeholde et tal mindre end eller lig med {param}.';
$lang['form_validation_greater_than']		= '{field} feltet skal indeholde et tal større end {param}.';
$lang['form_validation_greater_than_equal_to']	= '{field} feltet skal indeholde et tal større end eller lig med {param}.';
$lang['form_validation_error_message_not_set']	= 'Finder ikke en fejlmeddelelse, der svarer til dit feltnavn {field}.';
$lang['form_validation_in_list']		= '{field} feltet skal være en af: {param}.';
