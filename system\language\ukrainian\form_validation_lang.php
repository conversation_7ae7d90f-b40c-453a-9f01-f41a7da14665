<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'Поле {field} обов’язкове.';
$lang['form_validation_isset'] = 'Поле {field} повинно бути заповнене.';
$lang['form_validation_valid_email'] = 'В полі {field} повинна бути правильна E-mail адреса.';
$lang['form_validation_valid_emails'] = 'В полі {field} повинні бути правильні E-mail адреси.';
$lang['form_validation_valid_url'] = 'В полі {field} повинен бути правильний URL.';
$lang['form_validation_valid_ip'] = 'В полі {field} повинен бути правильний IP.';
$lang['form_validation_min_length'] = 'Довжина поля {field} повинна бути не менш, ніж {param} символів.';
$lang['form_validation_max_length'] = 'Довжина поля {field} не може перевищувати {param} символів.';
$lang['form_validation_exact_length'] = 'Довжина поля {field} повинна складати {param} символів.';
$lang['form_validation_alpha'] = 'Поле {field} може бути заповнене лише літерами.';
$lang['form_validation_alpha_numeric'] = 'Поле {field} може бути заповнене літерами та цифрами.';
$lang['form_validation_alpha_numeric_spaces'] = 'Поле {field} може бути заповнене літерами, цифрами та пробілами.';
$lang['form_validation_alpha_dash'] = 'Поле {field} може бути заповнене літерами, цифрами, знаків підкреслення та тире.';
$lang['form_validation_numeric'] = 'Поле {field} може складатися лише із цифр.';
$lang['form_validation_is_numeric'] = 'Поле {field} може складатися тільки з цифрових значень.';
$lang['form_validation_integer'] = 'Поле {field} може бути лише цілочисельним значенням.';
$lang['form_validation_regex_match'] = 'Поле {field} заповнено неправильно.';
$lang['form_validation_matches'] = 'Поле {field} не відповідає параметру {param}.';
$lang['form_validation_differs'] = 'Поле {field} повинно відрізнятися від параметра {param}.';
$lang['form_validation_is_unique'] = 'Поле {field} повинно бути унікальним.';
$lang['form_validation_is_natural'] = 'Поле {field} повинне мати лише цифри.';
$lang['form_validation_is_natural_no_zero'] = 'Поле {field} повинно мати лише цифри та бути більше нуля.';
$lang['form_validation_decimal'] = 'Поле {field} повинно мати десяткове значення.';
$lang['form_validation_less_than'] = 'Поле {field} повинно мати значення менше {param}.';
$lang['form_validation_less_than_equal_to'] = 'Поле {field} повинно мати значення, не більше {param}.';
$lang['form_validation_greater_than'] = 'Поле {field} повинно мати значення більше {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Поле {field} повинно мати значення, не менше ніж {param}.';
$lang['form_validation_error_message_not_set'] = 'Для поля {field} не встановлено повідомлення про помилку.';
$lang['form_validation_in_list'] = 'Поле {field} повинно бути одним з: {param}.';  
