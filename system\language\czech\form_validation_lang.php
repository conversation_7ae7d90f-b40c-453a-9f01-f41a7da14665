<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']              = 'Pole {field} je povinné.';
$lang['form_validation_isset']                 = 'Pole {field} musí obsahovat hodnotu.';
$lang['form_validation_valid_email']           = 'Pole {field} musí obsahovat validní emailovo adresu.';
$lang['form_validation_valid_emails']          = 'Pole {field} musí obsahovat validní emailovo adresy.';
$lang['form_validation_valid_url']             = 'Pole {field} musí obsahovat validní URL.';
$lang['form_validation_valid_ip']              = 'Pole {field} musí obsahovat validní IP.';
$lang['form_validation_min_length']            = 'Pole {field} musí obsahovat alespoň {param} znaků.';
$lang['form_validation_max_length']            = 'Pole {field} nesmí obsahovat více než {param} znaků.';
$lang['form_validation_exact_length']          = 'Pole {field} musí obsahovat přesně {param} znaků.';
$lang['form_validation_alpha']                 = 'Pole {field} může obsahovat pouze znaky abecedy.';
$lang['form_validation_alpha_numeric']         = 'Pole {field} může obsahovat pouze znaky abecedy a čísla.';
$lang['form_validation_alpha_numeric_spaces']  = 'Pole {field} může obsahovat pouze znaky abecedy, čísla a mezery.';
$lang['form_validation_alpha_dash']            = 'Pole {field} může obsahovat pouze znaky abecedy, čísla, podtržítka a pomlčky.';
$lang['form_validation_numeric']               = 'Pole {field} může obsahovat pouze čísla.';
$lang['form_validation_is_numeric']            = 'Pole {field} může obsahovat pouze číselné znaky.';
$lang['form_validation_integer']               = 'Pole {field} musí být celé číslo.';
$lang['form_validation_regex_match']           = 'Pole {field} není ve správném formátu.';
$lang['form_validation_matches']               = 'Pole {field} není shodné s polem {param}.';
$lang['form_validation_differs']               = 'Pole {field} musí být různé od pole {param}.';
$lang['form_validation_is_unique']             = 'Pole {field} musí obsahovat unikátní hodnotu.';
$lang['form_validation_is_natural']            = 'Pole {field} může obsahovat pouze přirozená čísla a nulu.';
$lang['form_validation_is_natural_no_zero']    = 'Pole {field} může obsahovat pouze přirozená čísla.';
$lang['form_validation_decimal']               = 'Pole {field} musí obsahovat desetinné čislo.';
$lang['form_validation_less_than']             = 'Pole {field} musí být menší než pole {param}.';
$lang['form_validation_less_than_equal_to']    = 'Pole {field} musí být menší nebo stejné jako pole {param}.';
$lang['form_validation_greater_than']          = 'Pole {field} musí být větší než pole {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Pole {field} musí být větší nebo stejné jako pole {param}.';
$lang['form_validation_error_message_not_set'] = 'Pro pole {field} není nastavena chybová zpráva.';
$lang['form_validation_in_list']               = 'Pole {field} musí být jedním z: {param}.'; 
