<?php return array(
    'root' => array(
        'name' => 'everjoe/everjoelab',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'clue/stream-filter' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'reference' => '3cd5ac57c18cf9e449682eeadd8f8fdd5b4e864f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/stream-filter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v5.5.1',
            'version' => '5.5.1.0',
            'reference' => '83b609028194aa042ea33b5af2d41a7427de80e6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/apiclient' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '2.7.0.0',
            'reference' => '48ec94577b51bde415270116118b07a294e07c43',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/apiclient-services' => array(
            'pretty_version' => 'v0.275.0',
            'version' => '0.275.0.0',
            'reference' => 'fe34eef7717556c2e64dfc37de3fde930a23a68f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient-services',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.23.1',
            'version' => '1.23.1.0',
            'reference' => 'cb43cb8ccde76ace65ec40130a9e9c2a15ecfe17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '8459341c16f96b9610dcdfe22bd3060d60c0da04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(
                0 => '7.5.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'b94b2807d85443f9719887892882d0329d1e2598',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(
                0 => '1.5.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'reference' => 'e98e3e6d4f86621a9b75f623996e6bbdeb4b9318',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/omnipay' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '3145f6f7a93f00c219bc1bf1a46ff25bb077defc',
            'type' => 'metapackage',
            'install_path' => NULL,
            'aliases' => array(
                0 => '3.2.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'moneyphp/money' => array(
            'pretty_version' => '3.x-dev',
            'version' => '3.9999999.9999999.9999999-dev',
            'reference' => '0dc40e3791c67e8793e3aa13fead8cf4661ec9cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../moneyphp/money',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.x-dev',
            'version' => '2.9999999.9999999.9999999-dev',
            'reference' => '1387e02612584ffa1a9e93384d2d63ba0a747e11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v7.1.9',
            'version' => '7.1.9.0',
            'reference' => 'a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'reference' => '14daed4296fae74d9e3201d2c4925d1acb7aa614',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'omnipay/common' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '*******',
            'reference' => 'e278ff00676c05cd0f4aaaf6189a226f26ae056e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../omnipay/common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'omnipay/paypal' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '67c96faa173124b4517989c2d6f1d1915afa0d27',
            'type' => 'library',
            'install_path' => __DIR__ . '/../omnipay/paypal',
            'aliases' => array(
                0 => '3.0.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'omnipay/stripe' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '20812498efedc1079baae5fea96567fefd669105',
            'type' => 'library',
            'install_path' => __DIR__ . '/../omnipay/stripe',
            'aliases' => array(
                0 => '3.2.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.99',
            'version' => '*********',
            'reference' => '84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.14.3',
            'version' => '1.14.3.0',
            'reference' => '31d8ee46d0215108df16a8527c7438e96a4d7735',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/guzzle7-adapter' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/guzzle7-adapter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/httplug' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'f640739f80dfa1152533976e3c112477f69274eb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(
                0 => '2.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'php-http/message' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'reference' => '7886e647a30a966a1a8d1dad1845b71ca8678361',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message-factory' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '597f30e6dfd32a85fd7dbe58cb47554b5bad910e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message-factory',
            'aliases' => array(
                0 => '1.0.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/promise' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '4c4c1f9b7289a2ec57cde7f1e9762a5789506f88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(
                0 => '1.1.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '2.0.x-dev',
            'version' => '2.0.9999999.9999999-dev',
            'reference' => '104f776cff186addf6ef0570d199bcfb9ea7ed7c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '22b2ef5687f43679481615605d7a15c557ce85b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(
                0 => '1.0.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'efd67d1dc14a7ef4fc4e518e7dee91c271d524e4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(
                0 => '1.0.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => '1.6.2',
            'version' => '1.6.2.0',
            'reference' => 'a6ad58897a6d97cc2d2cd2adaeda343b25a368ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'everjoe/everjoelab' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sonata-project/google-authenticator' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => 'feda53899b26af24e3db2fe7a3e5f053ca483762',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sonata-project/google-authenticator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => '2.5.x-dev',
            'version' => '2.5.9999999.9999999-dev',
            'reference' => 'e8b495ea28c1d97b5e0c121748d6f9b53d075c66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => '5.4.x-dev',
            'version' => '5.4.9999999.9999999-dev',
            'reference' => '5032c5849aef24741e1970cb03511b0dd131d838',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '8ad114f6b39e2c98a8b0e3bd907732c207c2b534',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(
                0 => '1.27.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(
                0 => '1.27.x-dev',
            ),
            'dev_requirement' => false,
        ),
    ),
);
