<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = 'Год';
$lang['date_years'] = 'Лет';
$lang['date_month'] = 'Месяц';
$lang['date_months'] = 'Месяцев';
$lang['date_week'] = 'Неделя';
$lang['date_weeks'] = 'Недель';
$lang['date_day'] = 'День';
$lang['date_days'] = 'Дней';
$lang['date_hour'] = 'Час';
$lang['date_hours'] = 'Часов';
$lang['date_minute'] = 'Минута';
$lang['date_minutes'] = 'Минут';
$lang['date_second'] = 'Секунда';
$lang['date_seconds'] = 'Секунд';

$lang['UM12'] = '(UTC -12:00) Бейкер/Хауленд';
$lang['UM11'] = '(UTC -11:00) Ниуэ';
$lang['UM10'] = '(UTC -10:00) Гавайи';
$lang['UM95'] = '(UTC -9:30) Маркизские острова';
$lang['UM9'] = '(UTC -9:00) Аляска';
$lang['UM8'] = '(UTC -8:00) Североамериканское и тихоокеанское время (США и Канада)';
$lang['UM7'] = '(UTC -7:00) Горное время (США и Канада), Мексика (Чиуауа, Ла-Пас, Мацатлан)';
$lang['UM6'] = '(UTC -6:00) Центральное время (США и Канада), Центральноамериканское время, Мексика (Гвадалахара, Мехико, Монтеррей';
$lang['UM5'] = '(UTC -5:00) Североамериканское восточное время (США и Канада), Южноамериканское тихоокеанское время (Богота, Лима, Кито)';
$lang['UM45'] = '(UTC -4:30) Каракас';
$lang['UM4'] = '(UTC -4:00) Атлантическое время (Канада), Южноамериканское тихоокеанское время, Ла-Пас, Сантьяго)';
$lang['UM35'] = '(UTC -3:30) Ньюфаундленд';
$lang['UM3'] = '(UTC -3:00) Южноамериканское восточное время (Аргентина, Бразилия, Буэнос-Айрес, Джорджтаун), Гренландия';
$lang['UM2'] = '(UTC -2:00) Среднеатлантическое время';
$lang['UM1'] = '(UTC -1:00) Азорские острова, Кабо-Верде';
$lang['UTC'] = '(UTC) Западноевропейское время (Дублин, Эдинбург, Лиссабон, Лондон, Касабланка, Монровия)';
$lang['UP1'] = '(UTC +1:00) Центральноевропейское время, Западное центральноафриканское время';
$lang['UP2'] = '(UTC +2:00) Калининградское время, Восточноевропейское время, Египет, Израиль, Ливан, Ливия, Турция, ЮАР';
$lang['UP3'] = '(UTC +3:00) Московское время, Восточноафриканское время, Ирак, Кувейт, Саудовская Аравия';
$lang['UP35'] = '(UTC +3:30) Тегеранское время';
$lang['UP4'] = '(UTC +4:00) Самарское время, ОАЭ, Оман, Азербайджан, Армения, Грузия';
$lang['UP45'] = '(UTC +4:30) Афганистан';
$lang['UP5'] = '(UTC +5:00) Екатеринбург, Западноазиатское время (Исламабад, Карачи, Узбекистан)';
$lang['UP55'] = '(UTC +5:30) Индия, Шри-Ланка';
$lang['UP575'] = '(UTC +5:45) Непал';
$lang['UP6'] = '(UTC +6:00) Омское время, Новосибирск, Кемерово, Центральноазиатское время (Бангладеш, Казахстан, Киргизия)';
$lang['UP65'] = '(UTC +6:30) Мьянма, Кокосовые острова';
$lang['UP7'] = '(UTC +7:00) Красноярское время, Юго-Восточная Азия (Бангкок, Джакарта, Ханой)';
$lang['UP8'] = '(UTC +8:00) Иркутское время, Улан-Батор, Куала-Лумпур, Гонконг, Китай, Сингапур, Тайвань, западноавстралийское время (Перт)';
$lang['UP875'] = '(UTC +8:45) Западноавстралийское время';
$lang['UP9'] = '(UTC +9:00) Якутское время, Корея, Япония';
$lang['UP95'] = '(UTC +9:30) Центральноавстралийское время';
$lang['UP10'] = '(UTC +10:00) Владивостокское время, Восточноавстралийское время';
$lang['UP105'] = '(UTC +10:30) Остров Лорд-Хау';
$lang['UP11'] = '(UTC +11:00) Среднеколымское время, Центрально-тихоокеанское время (Соломоновы Острова, Новая Каледония)';
$lang['UP115'] = '(UTC +11:30) Остров Норфолк';
$lang['UP12'] = '(UTC +12:00) Камчатское время, Маршалловы Острова, Фиджи, Новая Зеландия';
$lang['UP1275'] = '(UTC +12:45) Острова Чатем';
$lang['UP13'] = '(UTC +13:00) Самоа, Тонга';
$lang['UP14'] = '(UTC +14:00) Острова Лайн (Кирибати)';
