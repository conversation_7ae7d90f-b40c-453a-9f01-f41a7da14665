<?php
 /**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @copyright	Mario <PERSON>
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('Nije dozvoljen izravan pristup');

$lang['form_validation_required']		= 'Polje {field} je obavezno.';
$lang['form_validation_isset']			= 'Polje {field} mora imati vrijednost.';
$lang['form_validation_valid_email']	= 'Polje {field} mora sadržavati ispravnu email adresu.';
$lang['form_validation_valid_emails']	= 'Polje {field} mora sadržavati sve ispravne email adrese.';
$lang['form_validation_valid_url']		= 'Polje {field} mora sadržavati ispravan URL.';
$lang['form_validation_valid_ip']		= 'Polje {field} mora sadržavati ispravnu IP adresu.';
$lang['form_validation_min_length']		= 'Polje {field} mora biti najmanje {param} znakova dugo.';
$lang['form_validation_max_length']		= 'Polje {field} ne može imati veću dužinu od {param} znakova.';
$lang['form_validation_exact_length']	= 'Polje {field} mora imati dužinu točno {param} znakova.';
$lang['form_validation_alpha']			= 'Polje {field} mora sadržavati samo slovne znakove.';
$lang['form_validation_alpha_numeric']	= 'Polje {field} mora sadržavati samo alfa-numeričke znakove.';
$lang['form_validation_alpha_numeric_spaces']	= 'Polje {field} može sadržavati samo alfa-numeričke znakove i razmake.';
$lang['form_validation_alpha_dash']		= 'Polje {field} može sadržavati samo alfa-numeričke znakove, donje crte i srednje crte.';
$lang['form_validation_numeric']		= 'Polje {field} mora sadržavati samo brojeve.';
$lang['form_validation_is_numeric']		= 'Polje {field} mora sadržavati samo numeričke znakove.';
$lang['form_validation_integer']		= 'Polje {field} mora sadržavati bar jedan cijeli broj.';
$lang['form_validation_regex_match']	= 'Polje {field} nije u ispravnom formatu.';
$lang['form_validation_matches']		= 'Polje {field} se ne podudara sa {param} poljem.';
$lang['form_validation_differs']		= 'Polje {field} mora biti različito od polja {param}.';
$lang['form_validation_is_unique'] 		= 'Polje {field} mora sadržavati jedinstvenu vrijednost.';
$lang['form_validation_is_natural']		= 'Polje {field} mora sadržavati samo prirodne brojeve.';
$lang['form_validation_is_natural_no_zero']	= 'Polje {field} mora sadržavati samo prirodne brojeve i mora biti veće od nule.';
$lang['form_validation_decimal']		= 'Polje {field} mora sadržavati decimalni broj.';
$lang['form_validation_less_than']		= 'Polje {field} mora sadržavati broj manji od {param}.';
$lang['form_validation_less_than_equal_to']	= 'Polje {field} mora sadržavati broj manji ili jednak od {param}.';
$lang['form_validation_greater_than']		= 'Polje {field} mora sadržavati broj veći od {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Polje {field} mora sadržavati broj jednak ili veći od {param}.';
$lang['form_validation_error_message_not_set']	= 'Ne može se dohvatiti poruka o greški koja odgovara nazivu vašeg polja {field}.';
$lang['form_validation_in_list']		= 'Polje {field} mora biti jedno od: {param}.';
