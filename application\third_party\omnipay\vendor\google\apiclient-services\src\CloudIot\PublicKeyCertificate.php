<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudIot;

class PublicKeyCertificate extends \Google\Model
{
  /**
   * @var string
   */
  public $certificate;
  /**
   * @var string
   */
  public $format;
  protected $x509DetailsType = X509CertificateDetails::class;
  protected $x509DetailsDataType = '';

  /**
   * @param string
   */
  public function setCertificate($certificate)
  {
    $this->certificate = $certificate;
  }
  /**
   * @return string
   */
  public function getCertificate()
  {
    return $this->certificate;
  }
  /**
   * @param string
   */
  public function setFormat($format)
  {
    $this->format = $format;
  }
  /**
   * @return string
   */
  public function getFormat()
  {
    return $this->format;
  }
  /**
   * @param X509CertificateDetails
   */
  public function setX509Details(X509CertificateDetails $x509Details)
  {
    $this->x509Details = $x509Details;
  }
  /**
   * @return X509CertificateDetails
   */
  public function getX509Details()
  {
    return $this->x509Details;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(PublicKeyCertificate::class, 'Google_Service_CloudIot_PublicKeyCertificate');
