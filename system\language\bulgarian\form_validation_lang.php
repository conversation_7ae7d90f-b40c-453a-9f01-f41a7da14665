<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'Полето "{field}" е задължително.';
$lang['form_validation_isset'] = 'Полето "{field}" трябва да има стойност.';
$lang['form_validation_valid_email'] = 'Полето "{field}" трябва да съдържа валиден email адрес.';
$lang['form_validation_valid_emails'] = 'Полето "{field}" трябва да съдържа валидни email адреси.';
$lang['form_validation_valid_url'] = 'Полето "{field}" трябва да съдържа валиден URL адрес.';
$lang['form_validation_valid_ip'] = 'Полето "{field}" трябва да съдържа валиден IP адрес.';
$lang['form_validation_min_length'] = 'Полето "{field}" трябва да бъде дълго най-малко {param} символа.';
$lang['form_validation_max_length'] = 'Полето "{field}" трябва да бъде дълго най-много {param} символа.';
$lang['form_validation_exact_length'] = 'Полето "{field}" трябва да бъде дълго точно {param}s символа.';
$lang['form_validation_alpha'] = 'Полето "{field}" може да съдържа само латински букви.';
$lang['form_validation_alpha_numeric'] = 'Полето "{field}" може да съдържа само латински букви и цифри.';
$lang['form_validation_alpha_numeric_spaces'] = 'Полето "{field}" може да съдържа само латински букви, цифри и интервали.';
$lang['form_validation_alpha_dash'] = 'Полето "{field}" може да съдържа само латински букви, цифри, долна черта и тире.';
$lang['form_validation_numeric'] = 'Полето "{field}" трябва да е десетично число.';
$lang['form_validation_is_numeric'] = 'Полето "{field}" трябва да е число.';
$lang['form_validation_integer'] = 'Полето "{field}" може да съдържа само цeли числа.';
$lang['form_validation_regex_match'] = 'Полето "{field}" не е в правилен формат.';
$lang['form_validation_matches'] = 'Полето "{field}" не съвпада с полето "{param}".';
$lang['form_validation_differs'] = 'Полето "{field}" не трябва да съвпада с полето "{param}".';
$lang['form_validation_is_unique'] = 'Полето "{field}" трябва да съдържа уникална стойност.';
$lang['form_validation_is_natural'] = 'Полето "{field}" трябва да е цяло положително число или нула.';
$lang['form_validation_is_natural_no_zero'] = 'Полето "{field}" трябва да е цяло положително число.';
$lang['form_validation_decimal'] = 'Полето "{field}" трябва да съдържа десетично число.';
$lang['form_validation_less_than'] = 'Полето "{field}" трябва да съдържа число, по-малко от {param}.';
$lang['form_validation_less_than_equal_to'] = 'Полето "{field}" трябва да съдържа число, по-малко или равно на {param}.';
$lang['form_validation_greater_than'] = 'Полето "{field}" трябва да съдържа число, по-голямо от {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Полето "{field}" трябва да съдържа число, по-голямо или равно на {param}.';
$lang['form_validation_error_message_not_set'] = 'Грешка в полето "{field}". Описанието на грешката не е зададено.';
$lang['form_validation_in_list'] = 'Полето "{field}" трябва да е измежду стойностите {param}.';
