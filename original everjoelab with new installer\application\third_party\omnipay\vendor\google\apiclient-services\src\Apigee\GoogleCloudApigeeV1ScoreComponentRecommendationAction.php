<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Apigee;

class GoogleCloudApigeeV1ScoreComponentRecommendationAction extends \Google\Model
{
  protected $actionContextType = GoogleCloudApigeeV1ScoreComponentRecommendationActionActionContext::class;
  protected $actionContextDataType = '';
  /**
   * @var string
   */
  public $description;

  /**
   * @param GoogleCloudApigeeV1ScoreComponentRecommendationActionActionContext
   */
  public function setActionContext(GoogleCloudApigeeV1ScoreComponentRecommendationActionActionContext $actionContext)
  {
    $this->actionContext = $actionContext;
  }
  /**
   * @return GoogleCloudApigeeV1ScoreComponentRecommendationActionActionContext
   */
  public function getActionContext()
  {
    return $this->actionContext;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudApigeeV1ScoreComponentRecommendationAction::class, 'Google_Service_Apigee_GoogleCloudApigeeV1ScoreComponentRecommendationAction');
