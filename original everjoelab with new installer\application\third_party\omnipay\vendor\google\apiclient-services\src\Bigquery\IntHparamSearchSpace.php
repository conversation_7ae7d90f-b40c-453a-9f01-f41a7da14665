<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Bigquery;

class IntHparamSearchSpace extends \Google\Model
{
  protected $candidatesType = IntCandidates::class;
  protected $candidatesDataType = '';
  protected $rangeType = IntRange::class;
  protected $rangeDataType = '';

  /**
   * @param IntCandidates
   */
  public function setCandidates(IntCandidates $candidates)
  {
    $this->candidates = $candidates;
  }
  /**
   * @return IntCandidates
   */
  public function getCandidates()
  {
    return $this->candidates;
  }
  /**
   * @param IntRange
   */
  public function setRange(IntRange $range)
  {
    $this->range = $range;
  }
  /**
   * @return IntRange
   */
  public function getRange()
  {
    return $this->range;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(IntHparamSearchSpace::class, 'Google_Service_Bigquery_IntHparamSearchSpace');
