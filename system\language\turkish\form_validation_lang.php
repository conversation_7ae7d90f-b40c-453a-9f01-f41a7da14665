<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']               = '{field} alanı gerekli.';
$lang['form_validation_isset']                  = '{field} alanında bir değer olmalı.';
$lang['form_validation_valid_email']            = '{field} alanı geçerli bir email adresi içermeli.';
$lang['form_validation_valid_emails']           = '{field} alanı geçerli email adresleri içermeli.';
$lang['form_validation_valid_url']              = '{field} alanı geçerli bir URL içermeli.';
$lang['form_validation_valid_ip']               = '{field} alanı geçerli bir IP içermeli.';
$lang['form_validation_min_length']             = '{field} alanı en az {param} karakter uzunluğunda olmalı.';
$lang['form_validation_max_length']             = '{field} alanı {param} karakteri aşamaz.';
$lang['form_validation_exact_length']           = '{field} alanı tam {param} karakter uzunluğunda olmalı.';
$lang['form_validation_alpha']                  = '{field} alanı yalnız alfabetik karakterler içerebilir.';
$lang['form_validation_alpha_numeric']          = '{field} alanı yalnız alfabetik ve sayısal karakterler içerebilir.';
$lang['form_validation_alpha_numeric_spaces']   = '{field} alanı yalnız alfabetik, sayısal karakterler ve boşluklar içerebilir.';
$lang['form_validation_alpha_dash']             = '{field} alanı yalnız alfabetik, sayısal karakterler, alt çizgi ve tire işaretleri içerebilir.';
$lang['form_validation_numeric']                = '{field} alanı yalnız sayı içerebilir.';
$lang['form_validation_is_numeric']             = '{field} alanı yalnız sayısal karakterler içerebilir.';
$lang['form_validation_integer']                = '{field} alanı bir tamsayı içerebilir';
$lang['form_validation_regex_match']            = '{field} alanı doğru formatta değil.';
$lang['form_validation_matches']                = '{field} alanı ile {param} alanı eşleşmiyor.';
$lang['form_validation_differs']                = '{field} alanı {param} alanından farklı olmalı.';
$lang['form_validation_is_unique']              = '{field} alanı eşsiz bir değer içermeli.';
$lang['form_validation_is_natural']             = '{field} alanı yalnız rakamlar içerebilir.';
$lang['form_validation_is_natural_no_zero']     = '{field} alanı yalnız rakamlar içermeli ve sıfırdan büyük olmalı.';
$lang['form_validation_decimal']                = '{field} alanı bir onluk sayı içermeli.';
$lang['form_validation_less_than']              = '{field} alanı {param} den küçük bir sayı içermeli.';
$lang['form_validation_less_than_equal_to']     = '{field} alanı {param} den küçük veya eşit bir sayı içermeli.';
$lang['form_validation_greater_than']           = '{field} alanı {param} den büyük bir sayı içermeli.';
$lang['form_validation_greater_than_equal_to']  = '{field} alanı {param} den büyük veya eşit bir sayı içermeli.';
$lang['form_validation_error_message_not_set']  = '{field} alanı için bir hata mesajı bulunamadı.';
//$lang['form_validation_in_list']                = 'The {field} field must be one of: {param}.'; //FIXME
$lang['form_validation_in_list']                = '{field} alanı şunlardan biri olmalıdır: {param}'; //Turkish Translation
