<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = '要求含有 {field} 字段。';
$lang['form_validation_isset'] = '{field} 字段必须有值。';
$lang['form_validation_valid_email'] = '{field} 字段必须是一个有效的 E-mail 地址。';
$lang['form_validation_valid_emails'] = '{field} 字段包含的 E-mail 地址必须全部有效。';
$lang['form_validation_valid_url'] = '{field} 字段必须是一个有效的 URL。';
$lang['form_validation_valid_ip'] = '{field} 字段必须包含一个有效的 IP 地址。';
$lang['form_validation_min_length'] = '{field} 字段最少需要有 {param} 字的长度。';
$lang['form_validation_max_length'] = '{field} 字段不能超过 {param} 字的长度。';
$lang['form_validation_exact_length'] = '{field} 字段必须是 {param} 字的长度。';
$lang['form_validation_alpha'] = '{field} 字段取值只允许为字母。';
$lang['form_validation_alpha_numeric'] = '{field} 字段取值只允许为字母和数字。';
$lang['form_validation_alpha_numeric_spaces'] = '{field} 字段取值只允许为字母、数字和空格。';
$lang['form_validation_alpha_dash'] = '{field} 字段取值只允许为字母、数字、下划线和破折号';
$lang['form_validation_numeric'] = '{field} 字段取值只允许为数字。';
$lang['form_validation_is_numeric'] = '{field} 字段必须只包含数字。';
$lang['form_validation_integer'] = '{field} 字段必须是整数。';
$lang['form_validation_regex_match'] = '{field} 字段是错误的格式。';
$lang['form_validation_matches'] = '{field} 字段与 {param} 字段不匹配。';
$lang['form_validation_differs'] = '{field} 字段与 {param} 字段必须不同。';
$lang['form_validation_is_unique'] = '{field} 字段必须是一个独一无二的值。';
$lang['form_validation_is_natural'] = '{field} 字段必须是自然数。';
$lang['form_validation_is_natural_no_zero'] = '{field} 字段必须是非 0 的自然数。';
$lang['form_validation_decimal'] = '{field} 字段必须是十进制数。';
$lang['form_validation_less_than'] = '{field} 字段的值必须小于 {param}。';
$lang['form_validation_less_than_equal_to'] = '{field} 字段的值必须小于等于 {param}。';
$lang['form_validation_greater_than'] = '{field} 字段的值必须大于 {param}。';
$lang['form_validation_greater_than_equal_to'] = '{field} 字段的值必须大于等于 {param}。';
$lang['form_validation_error_message_not_set'] = '无法获得 {field} 字段的错误信息。';
$lang['form_validation_in_list'] = '{field} 字段必须是 {param} 中的一种。'; 
