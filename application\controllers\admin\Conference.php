<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Conference extends Admin_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model(array('video_conference_model', 'video_conference_settings_model', 'class_model', 'section_model', 'subject_model', 'staff_model', 'student_model'));
        $this->load->library(array('zoom_lib', 'gmeet_lib', 'jitsi_lib'));
    }

    public function index()
    {
        if (!$this->rbac->hasPrivilege('conference', 'can_view')) {
            access_denied();
        }

        $data['title'] = 'Video Conferences';
        $data['conferences'] = $this->video_conference_model->get();
        $data['upcoming'] = $this->video_conference_model->getUpcoming(5);
        $data['ongoing'] = $this->video_conference_model->getOngoing();
        
        $this->load->view('layout/header', $data);
        $this->load->view('admin/conference/index', $data);
        $this->load->view('layout/footer', $data);
    }

    public function create()
    {
        if (!$this->rbac->hasPrivilege('conference', 'can_add')) {
            access_denied();
        }

        $data['title'] = 'Create Video Conference';
        $data['classes'] = $this->class_model->get();
        $data['subjects'] = $this->subject_model->get();
        $data['staff'] = $this->staff_model->get();
        $data['platforms'] = $this->video_conference_settings_model->getActive();

        if ($this->input->post()) {
            $this->_create_conference();
        } else {
            $this->load->view('layout/header', $data);
            $this->load->view('admin/conference/create', $data);
            $this->load->view('layout/footer', $data);
        }
    }

    private function _create_conference()
    {
        $this->form_validation->set_rules('title', 'Title', 'required|trim');
        $this->form_validation->set_rules('platform', 'Platform', 'required|in_list[zoom,gmeet,jitsi]');
        $this->form_validation->set_rules('start_time', 'Start Time', 'required');
        $this->form_validation->set_rules('duration', 'Duration', 'required|numeric');
        $this->form_validation->set_rules('host_id', 'Host', 'required|numeric');

        if ($this->form_validation->run() == FALSE) {
            $this->create();
            return;
        }

        $platform = $this->input->post('platform');
        $meetingData = array(
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'start_time' => $this->input->post('start_time'),
            'duration' => $this->input->post('duration'),
            'host_name' => $this->input->post('host_name'),
            'host_email' => $this->input->post('host_email')
        );

        // Create meeting using appropriate platform
        $result = $this->_create_platform_meeting($platform, $meetingData);

        if (isset($result['success']) && $result['success']) {
            $conferenceData = array(
                'title' => $meetingData['title'],
                'description' => $meetingData['description'],
                'platform' => $platform,
                'meeting_id' => $result['meeting_id'],
                'meeting_url' => $result['meeting_url'],
                'password' => $result['password'] ?? null,
                'host_id' => $this->input->post('host_id'),
                'class_id' => $this->input->post('class_id') ?: null,
                'section_id' => $this->input->post('section_id') ?: null,
                'subject_id' => $this->input->post('subject_id') ?: null,
                'start_time' => $meetingData['start_time'],
                'end_time' => date('Y-m-d H:i:s', strtotime($meetingData['start_time']) + ($meetingData['duration'] * 60)),
                'duration' => $meetingData['duration'],
                'status' => 'scheduled',
                'is_recurring' => $this->input->post('is_recurring') ? 1 : 0,
                'recurring_pattern' => $this->input->post('recurring_pattern'),
                'max_participants' => $this->input->post('max_participants') ?: 100,
                'is_recorded' => $this->input->post('is_recorded') ? 1 : 0,
                'api_response' => json_encode($result['response']),
                'created_by' => $this->customlib->getStaffID()
            );

            $conference_id = $this->video_conference_model->add($conferenceData);

            if ($conference_id) {
                // Add participants if class/section is selected
                $this->_add_participants($conference_id);

                $this->session->set_flashdata('msg', '<div class="alert alert-success">Conference created successfully!</div>');
                redirect('admin/conference');
            } else {
                $this->session->set_flashdata('msg', '<div class="alert alert-danger">Failed to save conference to database!</div>');
            }
        } else {
            $error_msg = isset($result['error']) ? $result['error'] : 'Failed to create meeting';
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">' . $error_msg . '</div>');
        }

        $this->create();
    }

    private function _create_platform_meeting($platform, $meetingData)
    {
        switch ($platform) {
            case 'zoom':
                return $this->zoom_lib->createMeeting($meetingData);
            case 'gmeet':
                return $this->gmeet_lib->createMeeting($meetingData);
            case 'jitsi':
                return $this->jitsi_lib->createMeeting($meetingData);
            default:
                return array('error' => 'Invalid platform');
        }
    }

    private function _add_participants($conference_id)
    {
        $class_id = $this->input->post('class_id');
        $section_id = $this->input->post('section_id');

        if ($class_id && $section_id) {
            // Get all students in the class/section
            $students = $this->student_model->searchByClassSection($class_id, $section_id);

            foreach ($students as $student) {
                $participant_data = array(
                    'participant_type' => 'student',
                    'participant_id' => $student['id'],
                    'status' => 'invited'
                );
                $this->video_conference_model->addParticipant($conference_id, $participant_data);
            }
        }

        // Add host as participant
        $host_data = array(
            'participant_type' => 'staff',
            'participant_id' => $this->input->post('host_id'),
            'is_host' => 1,
            'status' => 'invited'
        );
        $this->video_conference_model->addParticipant($conference_id, $host_data);
    }

    public function edit($id)
    {
        if (!$this->rbac->hasPrivilege('conference', 'can_edit')) {
            access_denied();
        }

        $data['title'] = 'Edit Video Conference';
        $data['conference'] = $this->video_conference_model->get($id);
        $data['classes'] = $this->class_model->get();
        $data['subjects'] = $this->subject_model->get();
        $data['staff'] = $this->staff_model->get();
        $data['platforms'] = $this->video_conference_settings_model->getActive();

        if (!$data['conference']) {
            show_404();
        }

        if ($this->input->post()) {
            $this->_update_conference($id);
        } else {
            $this->load->view('layout/header', $data);
            $this->load->view('admin/conference/edit', $data);
            $this->load->view('layout/footer', $data);
        }
    }

    private function _update_conference($id)
    {
        $this->form_validation->set_rules('title', 'Title', 'required|trim');
        $this->form_validation->set_rules('start_time', 'Start Time', 'required');
        $this->form_validation->set_rules('duration', 'Duration', 'required|numeric');

        if ($this->form_validation->run() == FALSE) {
            $this->edit($id);
            return;
        }

        $conference = $this->video_conference_model->get($id);
        $meetingData = array(
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'start_time' => $this->input->post('start_time'),
            'duration' => $this->input->post('duration')
        );

        // Update meeting on platform
        $result = $this->_update_platform_meeting($conference->platform, $conference->meeting_id, $meetingData);

        if (isset($result['success']) && $result['success']) {
            $updateData = array(
                'title' => $meetingData['title'],
                'description' => $meetingData['description'],
                'start_time' => $meetingData['start_time'],
                'end_time' => date('Y-m-d H:i:s', strtotime($meetingData['start_time']) + ($meetingData['duration'] * 60)),
                'duration' => $meetingData['duration'],
                'class_id' => $this->input->post('class_id') ?: null,
                'section_id' => $this->input->post('section_id') ?: null,
                'subject_id' => $this->input->post('subject_id') ?: null,
                'max_participants' => $this->input->post('max_participants') ?: 100,
                'is_recorded' => $this->input->post('is_recorded') ? 1 : 0
            );

            if ($this->video_conference_model->update($id, $updateData)) {
                $this->session->set_flashdata('msg', '<div class="alert alert-success">Conference updated successfully!</div>');
                redirect('admin/conference');
            } else {
                $this->session->set_flashdata('msg', '<div class="alert alert-danger">Failed to update conference!</div>');
            }
        } else {
            $error_msg = isset($result['error']) ? $result['error'] : 'Failed to update meeting';
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">' . $error_msg . '</div>');
        }

        $this->edit($id);
    }

    private function _update_platform_meeting($platform, $meeting_id, $meetingData)
    {
        switch ($platform) {
            case 'zoom':
                return $this->zoom_lib->updateMeeting($meeting_id, $meetingData);
            case 'gmeet':
                return $this->gmeet_lib->updateMeeting($meeting_id, $meetingData);
            case 'jitsi':
                return $this->jitsi_lib->updateMeeting($meeting_id, $meetingData);
            default:
                return array('error' => 'Invalid platform');
        }
    }

    public function delete($id)
    {
        if (!$this->rbac->hasPrivilege('conference', 'can_delete')) {
            access_denied();
        }

        $conference = $this->video_conference_model->get($id);
        if (!$conference) {
            show_404();
        }

        // Delete from platform
        $result = $this->_delete_platform_meeting($conference->platform, $conference->meeting_id);

        // Delete from database regardless of platform result
        if ($this->video_conference_model->delete($id)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-success">Conference deleted successfully!</div>');
        } else {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">Failed to delete conference!</div>');
        }

        redirect('admin/conference');
    }

    private function _delete_platform_meeting($platform, $meeting_id)
    {
        switch ($platform) {
            case 'zoom':
                return $this->zoom_lib->deleteMeeting($meeting_id);
            case 'gmeet':
                return $this->gmeet_lib->deleteMeeting($meeting_id);
            case 'jitsi':
                return $this->jitsi_lib->deleteMeeting($meeting_id);
            default:
                return array('error' => 'Invalid platform');
        }
    }

    public function view($id)
    {
        if (!$this->rbac->hasPrivilege('conference', 'can_view')) {
            access_denied();
        }

        $data['title'] = 'Conference Details';
        $data['conference'] = $this->video_conference_model->get($id);
        $data['participants'] = $this->video_conference_model->getParticipants($id);

        if (!$data['conference']) {
            show_404();
        }

        $this->load->view('layout/header', $data);
        $this->load->view('admin/conference/view', $data);
        $this->load->view('layout/footer', $data);
    }

    public function join($id)
    {
        $conference = $this->video_conference_model->get($id);
        if (!$conference) {
            show_404();
        }

        // Check if user has permission to join
        $staff_id = $this->customlib->getStaffID();
        if ($conference->host_id != $staff_id && !$this->rbac->hasPrivilege('conference', 'can_view')) {
            access_denied();
        }

        // Update meeting status if it's starting time
        $current_time = time();
        $start_time = strtotime($conference->start_time);
        $end_time = strtotime($conference->end_time);

        if ($current_time >= $start_time && $current_time <= $end_time && $conference->status == 'scheduled') {
            $this->video_conference_model->updateStatus($id, 'ongoing');
        }

        // Redirect to meeting URL
        redirect($conference->meeting_url);
    }

    public function settings()
    {
        if (!$this->rbac->hasPrivilege('system_settings', 'can_view')) {
            access_denied();
        }

        $data['title'] = 'Video Conference Settings';
        $data['zoom_settings'] = $this->video_conference_settings_model->getZoomSettings();
        $data['gmeet_settings'] = $this->video_conference_settings_model->getGmeetSettings();
        $data['jitsi_settings'] = $this->video_conference_settings_model->getJitsiSettings();

        if ($this->input->post()) {
            $this->_save_settings();
        } else {
            $this->load->view('layout/header', $data);
            $this->load->view('admin/conference/settings', $data);
            $this->load->view('layout/footer', $data);
        }
    }

    private function _save_settings()
    {
        $platform = $this->input->post('platform');
        $settings_data = $this->input->post();
        unset($settings_data['platform']);

        $settings_data['updated_at'] = date('Y-m-d H:i:s');

        // Encrypt sensitive data
        $settings_data = $this->video_conference_settings_model->encryptSensitiveData($settings_data);

        if ($this->video_conference_settings_model->updateOrInsert($platform, $settings_data)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-success">Settings saved successfully!</div>');
        } else {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">Failed to save settings!</div>');
        }

        redirect('admin/conference/settings');
    }

    public function test_connection($platform)
    {
        $result = array('success' => false, 'message' => 'Unknown platform');

        switch ($platform) {
            case 'zoom':
                $result = $this->zoom_lib->testConnection();
                break;
            case 'gmeet':
                $result = $this->gmeet_lib->testConnection();
                break;
            case 'jitsi':
                $result = $this->jitsi_lib->testConnection();
                break;
        }

        echo json_encode($result);
    }

    public function reports()
    {
        if (!$this->rbac->hasPrivilege('conference', 'can_view')) {
            access_denied();
        }

        $data['title'] = 'Conference Reports';
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        $platform = $this->input->get('platform');

        $data['reports'] = $this->video_conference_model->getReports($start_date, $end_date, $platform);
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;
        $data['platform'] = $platform;

        $this->load->view('layout/header', $data);
        $this->load->view('admin/conference/reports', $data);
        $this->load->view('layout/footer', $data);
    }
}
