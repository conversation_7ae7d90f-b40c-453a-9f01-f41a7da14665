<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-video-camera"></i> <?php echo $this->lang->line('video_conferences'); ?>
            <small><?php echo $this->lang->line('manage_video_conferences'); ?></small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> <?php echo $this->lang->line('dashboard'); ?></a></li>
            <li class="active"><?php echo $this->lang->line('video_conferences'); ?></li>
        </ol>
    </section>

    <section class="content">
        <div class="row">
            <?php if ($this->session->flashdata('msg')) { echo $this->session->flashdata('msg'); } ?>
            
            <!-- Quick Stats -->
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-aqua">
                    <div class="inner">
                        <h3><?php echo count($upcoming); ?></h3>
                        <p><?php echo $this->lang->line('upcoming_meetings'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-clock-o"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-green">
                    <div class="inner">
                        <h3><?php echo count($ongoing); ?></h3>
                        <p><?php echo $this->lang->line('ongoing_meetings'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-play-circle"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-yellow">
                    <div class="inner">
                        <h3><?php echo count($conferences); ?></h3>
                        <p><?php echo $this->lang->line('total_conferences'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-video-camera"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-red">
                    <div class="inner">
                        <h3>3</h3>
                        <p><?php echo $this->lang->line('platforms_available'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-cogs"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ongoing Meetings -->
        <?php if (!empty($ongoing)) { ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-play-circle"></i> <?php echo $this->lang->line('ongoing_meetings'); ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('title'); ?></th>
                                        <th><?php echo $this->lang->line('platform'); ?></th>
                                        <th><?php echo $this->lang->line('host'); ?></th>
                                        <th><?php echo $this->lang->line('class'); ?></th>
                                        <th><?php echo $this->lang->line('start_time'); ?></th>
                                        <th><?php echo $this->lang->line('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ongoing as $meeting) { ?>
                                    <tr>
                                        <td><?php echo $meeting->title; ?></td>
                                        <td>
                                            <span class="label label-<?php echo $meeting->platform == 'zoom' ? 'primary' : ($meeting->platform == 'gmeet' ? 'success' : 'info'); ?>">
                                                <?php echo strtoupper($meeting->platform); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $meeting->host_name . ' ' . $meeting->host_surname; ?></td>
                                        <td><?php echo $meeting->class . ' (' . $meeting->section . ')'; ?></td>
                                        <td><?php echo date('M d, Y H:i', strtotime($meeting->start_time)); ?></td>
                                        <td>
                                            <a href="<?php echo base_url('admin/conference/join/' . $meeting->id); ?>" class="btn btn-success btn-xs" target="_blank">
                                                <i class="fa fa-play"></i> <?php echo $this->lang->line('join'); ?>
                                            </a>
                                            <a href="<?php echo base_url('admin/conference/view/' . $meeting->id); ?>" class="btn btn-info btn-xs">
                                                <i class="fa fa-eye"></i> <?php echo $this->lang->line('view'); ?>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php } ?>

        <!-- Main Conference List -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-video-camera"></i> <?php echo $this->lang->line('all_conferences'); ?></h3>
                        <div class="box-tools pull-right">
                            <?php if ($this->rbac->hasPrivilege('conference', 'can_add')) { ?>
                            <a href="<?php echo base_url('admin/conference/create'); ?>" class="btn btn-primary btn-sm">
                                <i class="fa fa-plus"></i> <?php echo $this->lang->line('create_conference'); ?>
                            </a>
                            <?php } ?>
                            <a href="<?php echo base_url('admin/conference/settings'); ?>" class="btn btn-default btn-sm">
                                <i class="fa fa-cog"></i> <?php echo $this->lang->line('settings'); ?>
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover" id="conference-table">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('title'); ?></th>
                                        <th><?php echo $this->lang->line('platform'); ?></th>
                                        <th><?php echo $this->lang->line('host'); ?></th>
                                        <th><?php echo $this->lang->line('class'); ?></th>
                                        <th><?php echo $this->lang->line('subject'); ?></th>
                                        <th><?php echo $this->lang->line('start_time'); ?></th>
                                        <th><?php echo $this->lang->line('duration'); ?></th>
                                        <th><?php echo $this->lang->line('status'); ?></th>
                                        <th class="text-right"><?php echo $this->lang->line('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($conferences)) { ?>
                                        <?php foreach ($conferences as $conference) { ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $conference->title; ?></strong>
                                                <?php if ($conference->description) { ?>
                                                <br><small class="text-muted"><?php echo substr($conference->description, 0, 50) . '...'; ?></small>
                                                <?php } ?>
                                            </td>
                                            <td>
                                                <span class="label label-<?php echo $conference->platform == 'zoom' ? 'primary' : ($conference->platform == 'gmeet' ? 'success' : 'info'); ?>">
                                                    <?php echo strtoupper($conference->platform); ?>
                                                </span>
                                            </td>
                                            <td><?php echo $conference->host_name . ' ' . $conference->host_surname; ?></td>
                                            <td>
                                                <?php if ($conference->class && $conference->section) { ?>
                                                    <?php echo $conference->class . ' (' . $conference->section . ')'; ?>
                                                <?php } else { ?>
                                                    <span class="text-muted"><?php echo $this->lang->line('all_classes'); ?></span>
                                                <?php } ?>
                                            </td>
                                            <td><?php echo $conference->subject_name ?: '-'; ?></td>
                                            <td><?php echo date('M d, Y H:i', strtotime($conference->start_time)); ?></td>
                                            <td><?php echo $conference->duration; ?> <?php echo $this->lang->line('minutes'); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch ($conference->status) {
                                                    case 'scheduled': $status_class = 'label-warning'; break;
                                                    case 'ongoing': $status_class = 'label-success'; break;
                                                    case 'completed': $status_class = 'label-default'; break;
                                                    case 'cancelled': $status_class = 'label-danger'; break;
                                                }
                                                ?>
                                                <span class="label <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($conference->status); ?>
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown">
                                                        <?php echo $this->lang->line('action'); ?> <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu pull-right" role="menu">
                                                        <li><a href="<?php echo base_url('admin/conference/view/' . $conference->id); ?>"><i class="fa fa-eye"></i> <?php echo $this->lang->line('view'); ?></a></li>
                                                        <?php if ($conference->status == 'scheduled' || $conference->status == 'ongoing') { ?>
                                                        <li><a href="<?php echo base_url('admin/conference/join/' . $conference->id); ?>" target="_blank"><i class="fa fa-play"></i> <?php echo $this->lang->line('join'); ?></a></li>
                                                        <?php } ?>
                                                        <?php if ($this->rbac->hasPrivilege('conference', 'can_edit')) { ?>
                                                        <li><a href="<?php echo base_url('admin/conference/edit/' . $conference->id); ?>"><i class="fa fa-edit"></i> <?php echo $this->lang->line('edit'); ?></a></li>
                                                        <?php } ?>
                                                        <?php if ($this->rbac->hasPrivilege('conference', 'can_delete')) { ?>
                                                        <li class="divider"></li>
                                                        <li><a href="<?php echo base_url('admin/conference/delete/' . $conference->id); ?>" onclick="return confirm('Are you sure?')"><i class="fa fa-trash"></i> <?php echo $this->lang->line('delete'); ?></a></li>
                                                        <?php } ?>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php } ?>
                                    <?php } else { ?>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted">
                                                <i class="fa fa-video-camera fa-2x"></i><br>
                                                <?php echo $this->lang->line('no_conferences_found'); ?>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    $('#conference-table').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "order": [[ 5, "desc" ]], // Sort by start time descending
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on action column
        ]
    });
});
</script>
