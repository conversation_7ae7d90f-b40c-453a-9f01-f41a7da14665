<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license		http://opensource.org/licenses/MIT	MIT License
 * @link		https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']				= '{field} mező megadása kötelező.';
$lang['form_validation_isset']					= '{field} mező nem lehet üres.';
$lang['form_validation_valid_email']			= '{field} mező csak érvényes email címet tartalmazhat.';
$lang['form_validation_valid_emails']			= '{field} mező csak érvényes email címeket tartalmazhat.';
$lang['form_validation_valid_url']				= '{field} mező csak érvényes URL címet tartalmazhat.';
$lang['form_validation_valid_ip']				= '{field} mező csak érvényes IP címet tartalmazhat.';
$lang['form_validation_min_length']				= '{field} mező legalább {param} karakter hosszú kell hogy legyen.';
$lang['form_validation_max_length']				= '{field} mező hossza nem haladhatja meg a(z) {param} karaktert.';
$lang['form_validation_exact_length']			= '{field} mező hossza pontosan {param} karakter kell hogy legyen.';
$lang['form_validation_alpha']					= '{field} mező csak az angol abc karaktereit tartalmazhatja.';
$lang['form_validation_alpha_numeric']			= '{field} mező alfanumerikus karaktereket tartalmazhat.';
$lang['form_validation_alpha_numeric_spaces']	= '{field} mező alfanumerikus karaktereket és szóközöket tartalmazhat.';
$lang['form_validation_alpha_dash']				= '{field} mező alfanumerikus karaktereket, alsóvonást és kötőjelet tartalmazhat.';
$lang['form_validation_numeric']				= '{field} mező csak számokat tartalmazhat.';
$lang['form_validation_is_numeric']				= '{field} mező csak numerikus karaktereket tartalmazhat.';
$lang['form_validation_integer']				= '{field} mező csak egészet tartalmazhat.';
$lang['form_validation_regex_match']			= '{field} mező nem megfelelő formátumú.';
$lang['form_validation_matches']				= '{field} mező nem azonos a(z) {param} mezővel.';
$lang['form_validation_differs']				= '{field} mező nem különböző a(z) {param} mezőtől.';
$lang['form_validation_is_unique'] 				= '{field} mező csak egyedi értéket tartalmazhat.';
$lang['form_validation_is_natural']				= '{field} mező csak nem negatív egész számokat tartalmazhat.';
$lang['form_validation_is_natural_no_zero']		= '{field} mező csak pozitív egész számokat tartalmazhat.';
$lang['form_validation_decimal']				= '{field} mező csak tizedes számokat tartalmazhat.';
$lang['form_validation_less_than']				= '{field} mező értéke kisebb, mint {param}.';
$lang['form_validation_less_than_equal_to']		= '{field} mező értéke kisebb vagy egyenlő, mint {param}.';
$lang['form_validation_greater_than']			= '{field} mező értéke nagyobb, mint {param}.';
$lang['form_validation_greater_than_equal_to']	= '{field} mező értéke nagyobb vagy egyenlő, mint {param}.';
$lang['form_validation_error_message_not_set']	= '{field} mezőhöz tartozó hibaüzenet nem érhető el!';
$lang['form_validation_in_list']				= '{field} mező értékének a következők egyikének kell lennie: {param}.';