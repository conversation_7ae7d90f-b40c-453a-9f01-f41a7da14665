<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @copyright	Pieter <PERSON>l
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('Directe toegang tot scripts is niet toegestaan');

$lang['form_validation_required']		= 'Het {field}-veld is verplicht.';
$lang['form_validation_isset']			= 'Het {field}-veld mag niet leeg zijn.';
$lang['form_validation_valid_email']		= 'Het {field}-veld dient een geldig e-mailadres te bevatten.';
$lang['form_validation_valid_emails']		= 'Het {field}-veld dient geldige e-mailadressen te bevatten.';
$lang['form_validation_valid_url']		= 'Het {field}-veld dient een geldige URL te bevatten.';
$lang['form_validation_valid_ip']		= 'Het {field}-veld dient een geldig IP-adres te bevatten.';
$lang['form_validation_min_length']		= 'Het {field}-veld dient tenminste {param} karakters lang te zijn.';
$lang['form_validation_max_length']		= 'Het {field}-veld mag niet meer dan {param} karaktertekens bevatten.';
$lang['form_validation_exact_length']		= 'Het {field}-veld dient precies {param} karaktertekens te bevatten.';
$lang['form_validation_alpha']			= 'Het {field}-veld mag alleen alfabetische karaktertekens bevatten.';
$lang['form_validation_alpha_numeric']		= 'Het {field}-veld mag alleen alfanumerieke karaktertekens bevatten.';
$lang['form_validation_alpha_numeric_spaces']	= 'Het {field}-veld mag alleen alfanumerieke karaktertekens en spaties bevatten.';
$lang['form_validation_alpha_dash']		= 'Het {field}-veld mag alleen alfanumerieke karaktertekens, underscores, en het minteken bevatten.';
$lang['form_validation_numeric']		= 'Het {field}-veld mag alleen cijfers bevatten.';
$lang['form_validation_is_numeric']		= 'Het {field}-veld mag alleen numerieke waarden bevatten.';
$lang['form_validation_integer']		= 'Het {field}-veld mag alleen hele getallen bevatten.';
$lang['form_validation_regex_match']		= 'Het {field}-veld heeft niet het juiste formaat.';
$lang['form_validation_matches']		= 'Het {field}-veld komt niet overeen met het {param}-veld.';
$lang['form_validation_differs']		= 'Het {field}-veld dient verschillend te zijn van het {param}-veld.';
$lang['form_validation_is_unique']		= 'Het {field}-veld dient een unieke waarde te bevatten.';
$lang['form_validation_is_natural']		= 'Het {field}-veld dient alleen cijfers te bevatten.';
$lang['form_validation_is_natural_no_zero']	= 'Het {field}-veld dient alleen cijfers groter dan nul te bevatten.';
$lang['form_validation_decimal']		= 'Het {field}-veld dient een decimaal nummer te bevatten.';
$lang['form_validation_less_than']		= 'Het {field}-veld dient een cijfer kleiner dan {param} te bevatten.';
$lang['form_validation_less_than_equal_to']	= 'Het {field}-veld dient een cijfer kleiner of gelijk aan {param} te bevatten.';
$lang['form_validation_greater_than']		= 'Het {field}-veld dient een cijfer groter dan {param} te bevatten.';
$lang['form_validation_greater_than_equal_to']	= 'Het {field}-veld dient een cijfer groter dan, of gelijk aan {param} te bevatten.';
$lang['form_validation_error_message_not_set']	= 'Tijdens de validatie van het {field}-veld is een niet nader genoemde fout opgetreden.';
$lang['form_validation_in_list']		= 'Het {field}-veld moet één van de volgende waardes bevatten: {param}.';