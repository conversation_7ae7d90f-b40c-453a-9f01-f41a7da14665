<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Apigee;

class GoogleCloudApigeeV1QueryTimeSeriesStatsRequest extends \Google\Collection
{
  protected $collection_key = 'metrics';
  /**
   * @var string[]
   */
  public $dimensions;
  /**
   * @var string
   */
  public $filter;
  protected $metricsType = GoogleCloudApigeeV1MetricAggregation::class;
  protected $metricsDataType = 'array';
  /**
   * @var int
   */
  public $pageSize;
  /**
   * @var string
   */
  public $pageToken;
  protected $timeRangeType = GoogleTypeInterval::class;
  protected $timeRangeDataType = '';
  /**
   * @var string
   */
  public $timestampOrder;
  /**
   * @var string
   */
  public $windowSize;

  /**
   * @param string[]
   */
  public function setDimensions($dimensions)
  {
    $this->dimensions = $dimensions;
  }
  /**
   * @return string[]
   */
  public function getDimensions()
  {
    return $this->dimensions;
  }
  /**
   * @param string
   */
  public function setFilter($filter)
  {
    $this->filter = $filter;
  }
  /**
   * @return string
   */
  public function getFilter()
  {
    return $this->filter;
  }
  /**
   * @param GoogleCloudApigeeV1MetricAggregation[]
   */
  public function setMetrics($metrics)
  {
    $this->metrics = $metrics;
  }
  /**
   * @return GoogleCloudApigeeV1MetricAggregation[]
   */
  public function getMetrics()
  {
    return $this->metrics;
  }
  /**
   * @param int
   */
  public function setPageSize($pageSize)
  {
    $this->pageSize = $pageSize;
  }
  /**
   * @return int
   */
  public function getPageSize()
  {
    return $this->pageSize;
  }
  /**
   * @param string
   */
  public function setPageToken($pageToken)
  {
    $this->pageToken = $pageToken;
  }
  /**
   * @return string
   */
  public function getPageToken()
  {
    return $this->pageToken;
  }
  /**
   * @param GoogleTypeInterval
   */
  public function setTimeRange(GoogleTypeInterval $timeRange)
  {
    $this->timeRange = $timeRange;
  }
  /**
   * @return GoogleTypeInterval
   */
  public function getTimeRange()
  {
    return $this->timeRange;
  }
  /**
   * @param string
   */
  public function setTimestampOrder($timestampOrder)
  {
    $this->timestampOrder = $timestampOrder;
  }
  /**
   * @return string
   */
  public function getTimestampOrder()
  {
    return $this->timestampOrder;
  }
  /**
   * @param string
   */
  public function setWindowSize($windowSize)
  {
    $this->windowSize = $windowSize;
  }
  /**
   * @return string
   */
  public function getWindowSize()
  {
    return $this->windowSize;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudApigeeV1QueryTimeSeriesStatsRequest::class, 'Google_Service_Apigee_GoogleCloudApigeeV1QueryTimeSeriesStatsRequest');
