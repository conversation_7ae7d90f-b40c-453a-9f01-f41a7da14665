<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Gmeet_lib
{
    protected $CI;
    protected $client_id;
    protected $client_secret;
    protected $access_token;
    protected $refresh_token;
    protected $base_url = 'https://www.googleapis.com/calendar/v3/';

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('video_conference_settings_model');
        $this->loadSettings();
    }

    private function loadSettings()
    {
        $settings = $this->CI->video_conference_settings_model->getGmeetSettings();
        if ($settings) {
            $settings = $this->CI->video_conference_settings_model->decryptSensitiveData($settings);
            $this->client_id = $settings->client_id;
            $this->client_secret = $settings->client_secret;
            $this->access_token = $settings->jwt_token; // Using jwt_token field for access token
        }
    }

    public function getAuthUrl($redirect_uri)
    {
        $params = array(
            'client_id' => $this->client_id,
            'redirect_uri' => $redirect_uri,
            'scope' => 'https://www.googleapis.com/auth/calendar.events',
            'response_type' => 'code',
            'access_type' => 'offline',
            'prompt' => 'consent'
        );

        return 'https://accounts.google.com/o/oauth2/auth?' . http_build_query($params);
    }

    public function exchangeCodeForToken($code, $redirect_uri)
    {
        $data = array(
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $redirect_uri
        );

        $response = $this->makeTokenRequest('https://oauth2.googleapis.com/token', $data);

        if (isset($response['access_token'])) {
            $this->access_token = $response['access_token'];
            $this->refresh_token = $response['refresh_token'] ?? null;
            
            // Save tokens to database
            $this->saveTokens($response['access_token'], $this->refresh_token);
            
            return ['success' => true, 'tokens' => $response];
        }

        return ['error' => 'Failed to exchange code for token', 'response' => $response];
    }

    private function makeTokenRequest($url, $data)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded'
            ]
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        return json_decode($response, true);
    }

    private function makeRequest($endpoint, $method = 'GET', $data = null)
    {
        if (empty($this->access_token)) {
            return ['error' => 'No access token available'];
        }

        $curl = curl_init();
        $url = $this->base_url . $endpoint;

        $headers = [
            'Authorization: Bearer ' . $this->access_token,
            'Content-Type: application/json'
        ];

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
        ]);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        $decoded = json_decode($response, true);
        $decoded['http_code'] = $httpCode;

        return $decoded;
    }

    public function createMeeting($meetingData)
    {
        $eventData = [
            'summary' => $meetingData['title'],
            'description' => $meetingData['description'] ?? '',
            'start' => [
                'dateTime' => date('c', strtotime($meetingData['start_time'])),
                'timeZone' => date_default_timezone_get()
            ],
            'end' => [
                'dateTime' => date('c', strtotime($meetingData['start_time']) + ($meetingData['duration'] * 60)),
                'timeZone' => date_default_timezone_get()
            ],
            'conferenceData' => [
                'createRequest' => [
                    'requestId' => uniqid(),
                    'conferenceSolutionKey' => [
                        'type' => 'hangoutsMeet'
                    ]
                ]
            ],
            'attendees' => []
        ];

        $response = $this->makeRequest('calendars/primary/events?conferenceDataVersion=1', 'POST', $eventData);

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['id'])) {
            $meetUrl = $response['conferenceData']['entryPoints'][0]['uri'] ?? '';
            $meetId = $response['conferenceData']['conferenceId'] ?? '';

            return [
                'success' => true,
                'meeting_id' => $meetId,
                'meeting_url' => $meetUrl,
                'event_id' => $response['id'],
                'response' => $response
            ];
        }

        return ['error' => 'Failed to create meeting', 'response' => $response];
    }

    public function updateMeeting($eventId, $meetingData)
    {
        $updateData = [
            'summary' => $meetingData['title'],
            'description' => $meetingData['description'] ?? '',
            'start' => [
                'dateTime' => date('c', strtotime($meetingData['start_time'])),
                'timeZone' => date_default_timezone_get()
            ],
            'end' => [
                'dateTime' => date('c', strtotime($meetingData['start_time']) + ($meetingData['duration'] * 60)),
                'timeZone' => date_default_timezone_get()
            ]
        ];

        $response = $this->makeRequest("calendars/primary/events/{$eventId}", 'PUT', $updateData);

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['id'])) {
            return ['success' => true, 'message' => 'Meeting updated successfully', 'event' => $response];
        }

        return ['error' => 'Failed to update meeting', 'response' => $response];
    }

    public function deleteMeeting($eventId)
    {
        $response = $this->makeRequest("calendars/primary/events/{$eventId}", 'DELETE');

        if ($response['http_code'] == 204) {
            return ['success' => true, 'message' => 'Meeting deleted successfully'];
        }

        return ['error' => 'Failed to delete meeting', 'response' => $response];
    }

    public function getMeeting($eventId)
    {
        $response = $this->makeRequest("calendars/primary/events/{$eventId}");

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['id'])) {
            return [
                'success' => true,
                'event' => $response
            ];
        }

        return ['error' => 'Event not found', 'response' => $response];
    }

    public function listMeetings($timeMin = null, $timeMax = null)
    {
        $params = array(
            'orderBy' => 'startTime',
            'singleEvents' => 'true'
        );

        if ($timeMin) {
            $params['timeMin'] = date('c', strtotime($timeMin));
        }
        if ($timeMax) {
            $params['timeMax'] = date('c', strtotime($timeMax));
        }

        $endpoint = 'calendars/primary/events?' . http_build_query($params);
        $response = $this->makeRequest($endpoint);

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['items'])) {
            return [
                'success' => true,
                'events' => $response['items']
            ];
        }

        return ['error' => 'No events found', 'response' => $response];
    }

    private function saveTokens($access_token, $refresh_token = null)
    {
        $data = array(
            'jwt_token' => $access_token,
            'updated_at' => date('Y-m-d H:i:s')
        );

        if ($refresh_token) {
            $data['api_secret'] = $refresh_token; // Using api_secret field for refresh token
        }

        $this->CI->video_conference_settings_model->update('gmeet', $data);
    }

    public function refreshAccessToken()
    {
        if (empty($this->refresh_token)) {
            return ['error' => 'No refresh token available'];
        }

        $data = array(
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'refresh_token' => $this->refresh_token,
            'grant_type' => 'refresh_token'
        );

        $response = $this->makeTokenRequest('https://oauth2.googleapis.com/token', $data);

        if (isset($response['access_token'])) {
            $this->access_token = $response['access_token'];
            $this->saveTokens($response['access_token']);
            
            return ['success' => true, 'access_token' => $response['access_token']];
        }

        return ['error' => 'Failed to refresh token', 'response' => $response];
    }

    public function testConnection()
    {
        $response = $this->makeRequest('calendars/primary');

        if (isset($response['error'])) {
            return ['success' => false, 'message' => $response['error']['message'] ?? 'Connection failed'];
        }

        if (isset($response['id'])) {
            return ['success' => true, 'message' => 'Connection successful', 'calendar' => $response];
        }

        return ['success' => false, 'message' => 'Connection failed', 'response' => $response];
    }

    public function isConfigured()
    {
        return !empty($this->client_id) && !empty($this->client_secret);
    }

    public function hasValidToken()
    {
        return !empty($this->access_token);
    }
}
