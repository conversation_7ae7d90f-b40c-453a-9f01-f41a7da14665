<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license http://opensource.org/licenses/MIT MIT License
 * @link https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= '{field}은(는) 필수입니다.';
$lang['form_validation_isset']			= '{field}은(는) 필수입니다.';
$lang['form_validation_valid_email']		= '{field}은(는) 유효한 이메일 주소 형식이 아닙니다.';
$lang['form_validation_valid_emails']		= '{field}은(는) 하나 이상의 이메일 주소가 유효한 형식이 아닙니다.';
$lang['form_validation_valid_url']		= '{field}은(는) 유효한 URL이 아닙니다.';
$lang['form_validation_valid_ip']		= '{field}은(는) 유효한 IP가 아닙니다.';
$lang['form_validation_min_length']		= '{field}은(는) 최소 {param}자 이상이어야 합니다.';
$lang['form_validation_max_length']		= '{field}은(는) 최대 {param}자 이하여야 합니다.';
$lang['form_validation_exact_length']		= '{field}은(는) 정확히 {param}자 이어야 합니다.';
$lang['form_validation_alpha']			= '{field}은(는) 영문이어야 합니다.';
$lang['form_validation_alpha_numeric']		= '{field}은(는) 영문-숫자 조합이어야 합니다.';
$lang['form_validation_alpha_numeric_spaces']	= '{field}은(는) 영문-숫자-공백 조합이어야 합니다.';
$lang['form_validation_alpha_dash']		= '{field}은(는) 영문-\'-\'-\'_\' 조합이어야 합니다.';
$lang['form_validation_numeric']		= '{field}은(는) 숫자이어야 합니다.';
$lang['form_validation_is_numeric']		= '{field}은(는) 반드시 숫자를 포함하여야 합니다.';
$lang['form_validation_integer']		= '{field}은(는) 반드시 정수여야 합니다.';
$lang['form_validation_regex_match']		= '{field}은(는) 유효한 입력값이 아닙니다.';
$lang['form_validation_matches']		= '{field}은(는) {param}와(과) 일치하지 않습니다.';
$lang['form_validation_differs']		= '{field}은(는) {param}와(과) 일치하지 않아야 합니다.';
$lang['form_validation_is_unique'] 		= '{field}은(는) 고윳값이 아닙니다.';
$lang['form_validation_is_natural']		= '{field}은(는) 반드시 수를 포함하여야 합니다.';
$lang['form_validation_is_natural_no_zero']	= '{field}은(는) 반드시 숫자를 포함하고, 0보다 커야 합니다.';
$lang['form_validation_decimal']		= '{field}은(는) 반드시 소수여야 합니다.';
$lang['form_validation_less_than']		= '{field}은(는) {param}보다 작아야 합니다.';
$lang['form_validation_less_than_equal_to']	= '{field}은(는) {param}보다 작거나 같아야 합니다.';
$lang['form_validation_greater_than']		= '{field}은(는) {param}보다 커야 합니다.';
$lang['form_validation_greater_than_equal_to']	= '{field}은(는) {param}보다 크거나 같아야 합니다.';
$lang['form_validation_error_message_not_set']	= '{field} 필드의 에러메시지가 설정되어있지 않습니다.';
$lang['form_validation_in_list']		= '{field} 필드는 반드시 다음 중 하나와 일치해야 합니다 : {param}';
