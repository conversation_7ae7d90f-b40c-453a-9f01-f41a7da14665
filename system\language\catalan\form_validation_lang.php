<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'El camp {field} és obligatori.';
$lang['form_validation_isset'] = 'El camp {field} ha de tenir un valor.';
$lang['form_validation_valid_email'] = 'El camp {field} ha de contenir un email vàlid.';
$lang['form_validation_valid_emails'] = 'El camp {field} ha de contenir adreçes email vàlides.';
$lang['form_validation_valid_url'] = 'El camp {field} ha de contenir una URL vàlida.';
$lang['form_validation_valid_ip'] = 'El camp {field} ha de contenir una IP vàlida.';
$lang['form_validation_min_length'] = 'El camp {field} ha de tenir com a mínim {param} caràcters.';
$lang['form_validation_max_length'] = 'El camp {field} no pot excedir els {param} caràcters.';
$lang['form_validation_exact_length'] = 'El camp {field} ha de tenir exactament {param} caràcters.';
$lang['form_validation_alpha'] = 'El camp {field} només pot contenir caràcters alfabètics.';
$lang['form_validation_alpha_numeric'] = 'El camp {field} només pot contenir caràcters alfanumèrics.';
$lang['form_validation_alpha_numeric_spaces'] = 'El camp {field} només pot contenir caràcters alfanumèrics i espais.';
$lang['form_validation_alpha_dash'] = 'El camp {field} només pot contenir caràcters alfanumèrics i espais, guions i guins baixos.';
$lang['form_validation_numeric'] = 'El camp {field} només pot contenir números.';
$lang['form_validation_is_numeric'] = 'El camp {field} només pot contenir caràcters numèrics.';
$lang['form_validation_integer'] = 'El camp {field} ha de contenir un enter.';
$lang['form_validation_regex_match'] = 'El camp {field} no conté un format correcte.';
$lang['form_validation_matches'] = 'El camp {field} no coincideix amb el camp {param}.';
$lang['form_validation_differs'] = 'El camp {field} ha de ser diferent del camp {param}.';
$lang['form_validation_is_unique'] = 'El camp {field} ha de contenir un valor únic.';
$lang['form_validation_is_natural'] = 'El camp {field} només pot contenir digits.';
$lang['form_validation_is_natural_no_zero'] = 'El camp {field} només pot contenir digits i ha de ser més gran que zero.';
$lang['form_validation_decimal'] = 'El camp {field} ha de contenir un nombre decimal.';
$lang['form_validation_less_than'] = 'El camp {field} ha de contenir un nombre més petit que {param}.';
$lang['form_validation_less_than_equal_to'] = 'El camp {field} ha de contenir un nombre més petit o igual que {param}.';
$lang['form_validation_greater_than'] = 'El camp {field} ha de contenir un nombre més gran que {param}.';
$lang['form_validation_greater_than_equal_to'] = 'El camp {field} ha de contenir un nombre més gran o igual que {param}.';
$lang['form_validation_error_message_not_set'] = 'Impossible accedir a un missatge d\'error corresponent al camp {field}.';
$lang['form_validation_in_list'] = 'El camp {field} ha de ser un d\'aquests: {param}.';
