<?php
/**
* System messages translation for CodeIgniter(tm)
*
* <AUTHOR> community
* @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
* @license	http://opensource.org/licenses/MIT MIT License
* @link	https://codeigniter.com
*/
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Ang {field} field ay kinakailangan.';
$lang['form_validation_isset']			= 'Ang {field} field ay dapat may laman.';
$lang['form_validation_valid_email']		= 'Ang {field} field ay dapat na maglaman ng isang wastong email address.';
$lang['form_validation_valid_emails']		= 'The {field} field ay dapat na maglaman ang lahat ng mga wastong email address.';
$lang['form_validation_valid_url']		= 'Ang {field} field ay dapat maglaman ng wastong URL.';
$lang['form_validation_valid_ip']		= 'Ang {field} field ay dapat maglaman ng wastong IP.';
$lang['form_validation_min_length']		= 'Ang {field} field ay dpat hindi bababa sa {param} character ang haba.';
$lang['form_validation_max_length']		= 'Ang {field} field ay dpat hindi lumagpas sa {param} character ang haba.';
$lang['form_validation_exact_length']		= 'Ang {field} field ay dapat eksakto sa {param} character ang haba.';
$lang['form_validation_alpha']			= 'Ang {field} field ay maaari lamang maglaman ng mga alpabetikong character.';
$lang['form_validation_alpha_numeric']		= 'Ang {field} field ay maaari lamang maglaman ng mga alpha-numeric character.';
$lang['form_validation_alpha_numeric_spaces']	= 'Ang {field} field ay maaari lamang maglaman ng mga alpha-numeric character at puwang.';
$lang['form_validation_alpha_dash']		= 'Ang {field} field ay maaari lamang maglaman ng mga alpha-numeric character, underscore, at gitling.';
$lang['form_validation_numeric']		= 'Ang {field} field ay dapat lamang maglaman ng mga numero.';
$lang['form_validation_is_numeric']		= 'Ang {field} field ay dapat lamang maglaman ng mga numeric character.';
$lang['form_validation_integer']		= 'Ang {field} field ay dapat maglaman ng isang integer.';
$lang['form_validation_regex_match']		= 'Ang {field} field ay wala sa tamang format.';
$lang['form_validation_matches']		= 'Ang {field} field ay hindi tumutugma sa {param} field.';
$lang['form_validation_differs']		= 'Ang {field} field ay dapat iba sa {param} field.';
$lang['form_validation_is_unique'] 		= 'Ang {field} field ay dapat maglaman ng isang unique value.';
$lang['form_validation_is_natural']		= 'Ang {field} field ay dapat lamang maglaman ng mga numero.';
$lang['form_validation_is_natural_no_zero']	= 'Ang {field} field ay dapat lamang maglaman ng mga numero na mas higit sa sero.';
$lang['form_validation_decimal']		= 'Ang {field} field ay dapat maglaman ng mga desimal na numero.';
$lang['form_validation_less_than']		= 'Ang {field} field ay dapat maglaman ng numero na mas mababa sa {param}.';
$lang['form_validation_less_than_equal_to']	= 'Ang {field} field ay dapat maglaman ng numero na mas mababa o katumbas ng {param}.';
$lang['form_validation_greater_than']		= 'Ang {field} field ay dapat maglaman ng numero na mas higit sa {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Ang {field} field ay dapat maglaman ng numero na mas higit o katumbas ng {param}.';
$lang['form_validation_error_message_not_set']	= 'Hindi ma-access ang isang error message na nauukol sa field na may pangalang {field}.';
$lang['form_validation_in_list']		= 'Ang {field} field ay dapat isa sa mga: {param}.';
