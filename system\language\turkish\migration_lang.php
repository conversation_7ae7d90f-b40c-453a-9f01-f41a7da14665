<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['migration_none_found']           = 'Herhangi bir taşınma bulunamadı.';
$lang['migration_not_found']            = 'Belirtilen sürüm numaralı taşınma bulunamadı: %s.';
$lang['migration_sequence_gap']         = 'Şu sürüm numarasına yakın taşınma sıralamasında bir boşluk var: %s.';
$lang['migration_multiple_version']     = 'Aynı sürüm numaralı birden çok taşınma var: %s.';
$lang['migration_class_doesnt_exist']   = 'Taşınma sınıfı "%s" bulunamadı.';
$lang['migration_missing_up_method']    = '"%s" taşınma sınıfında "up" metodu eksik.';
$lang['migration_missing_down_method']  = '"%s" taşınma sınıfında "down" metodu eksik.';
$lang['migration_invalid_filename']     = '"%s" taşınmasında geçersiz dosya adı.';
