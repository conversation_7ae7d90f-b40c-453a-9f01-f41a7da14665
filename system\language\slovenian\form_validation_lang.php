<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Polje {field} je obvezno.';
$lang['form_validation_isset']			= 'Polje {field} mora imeti vrednost.';
$lang['form_validation_valid_email']		= 'Polje {field} mora vsebovat veljavni e-poštni naslov.';
$lang['form_validation_valid_emails']		= 'Polje {field} mora vsebovati veljavne e-poštne naslove.';
$lang['form_validation_valid_url']		= 'Polje {field} mora vsebovati veljavni URL.';
$lang['form_validation_valid_ip']		= 'Polje {field} mora vsebovati veljavni IP.';
$lang['form_validation_min_length']		= 'Dolžina polja {field} mora biti vsaj {param}.';
$lang['form_validation_max_length']		= 'Dolžina polja {field} ne more biti več kot {param}.';
$lang['form_validation_exact_length']		= 'Dolžina polja {field} mora biti natančno {param}.';
$lang['form_validation_alpha']			= 'Polje {field} lahko vsebuje samo črke.';
$lang['form_validation_alpha_numeric']		= 'Polje {field} lahko vsebuje samo črke in številke.';
$lang['form_validation_alpha_numeric_spaces']	= 'Polje {field} lahko vsebuje samo črke,številke in presledke.';
$lang['form_validation_alpha_dash']		= 'Polje {field} lahko vsebuje samo črke, številke, podčrtaje in pomišljaje.';
$lang['form_validation_numeric']		= 'Polje {field} lahko vsebuje samo številke.';
$lang['form_validation_is_numeric']		= 'Polje {field} lahko vsebuje samo številke.';
$lang['form_validation_integer']		= 'Polje {field} lahko vsebuje samo celo število.';
$lang['form_validation_regex_match']		= 'Polje {field} ni v pravilni obliki.';
$lang['form_validation_matches']		= 'Polje {field} ni enako polje {param}.';
$lang['form_validation_differs']		= 'Polje {field} mora biti različno od polja {param}.';
$lang['form_validation_is_unique'] 		= 'Polje {field} mora vsebovati unikatno vrednosti.';
$lang['form_validation_is_natural']		= 'Polje {field} lahko vsebuje samo številke.';
$lang['form_validation_is_natural_no_zero']	= 'Polje {field} lahko vsebuje samo številke in mora biti večje od nič.';
$lang['form_validation_decimal']		= 'Polje {field} mora vsebovati decimalno številko.';
$lang['form_validation_less_than']		= 'Polje {field} mora vsebovati številko manjšo od {param}.';
$lang['form_validation_less_than_equal_to']	= 'Polje {field} mora vsebovati številko enako ali manjšo od {param}.';
$lang['form_validation_greater_than']		= 'Polje {field} mora vsebovati številko večjo od {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Polje {field} mora vsebovati številko enako ali večjo od {param}.';
$lang['form_validation_error_message_not_set']	= 'Ni možno dostopati do sporočila napake za ustrezno polje {field}.';
$lang['form_validation_in_list'] = 'Polje {polje} mora biti eno izmed: {param}.';
