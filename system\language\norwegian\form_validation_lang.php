<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= '{field} må fylles inn.';
$lang['form_validation_isset']			= '{field} må fylles inn.';
$lang['form_validation_valid_email']	= '{field} må inneholde en gyldig e-postadresse.';
$lang['form_validation_valid_emails']	= '{field} kan kun inneholde gyldige e-postadresser.';
$lang['form_validation_valid_url']		= '{field} må inneholde en gyldig internettadresse.';
$lang['form_validation_valid_ip']		= '{field} må inneholde en gyldig IP-adresse.';
$lang['form_validation_min_length']		= '{field} må inneholde minst {param} tegn.';
$lang['form_validation_max_length']		= '{field} kan maksimalt inneholde {param} tegn.';
$lang['form_validation_exact_length']	= '{field} må inneholde nøyaktig {param} tegn.';
$lang['form_validation_alpha']			= '{field} kan kun inneholde bokstaver.';
$lang['form_validation_alpha_numeric']	= '{field} kan kun inneholde bokstaver og tall.';
$lang['form_validation_alpha_numeric_spaces']	= '{field} kan kun inneholde bokstaver, tall og mellomrom.';
$lang['form_validation_alpha_dash']		= '{field} kan kun inneholde bokstaver, tall, understreker og bindestreker.';
$lang['form_validation_numeric']		= '{field} kan kun inneholde tall.';
$lang['form_validation_is_numeric']		= '{field} kan kun inneholde tall.';
$lang['form_validation_integer']		= '{field} må inneholde et heltall.';
$lang['form_validation_regex_match']		= '{field} er ikke i rett format.';
$lang['form_validation_matches']		= '{field} field does not match the {param} field.';
$lang['form_validation_differs']		= '{field} må være forskjellig fra {param}.';
$lang['form_validation_is_unique'] 		= '{field} må inneholde en unik verdi.';
$lang['form_validation_is_natural']		= '{field} kan kun inneholde tall.';
$lang['form_validation_is_natural_no_zero']	= '{field} kan kun inneholde tall og må være større enn null.';
$lang['form_validation_decimal']		= '{field} må inneholde et desimaltall.';
$lang['form_validation_less_than']		= '{field} må inneholde et tall mindre enn {param}.';
$lang['form_validation_less_than_equal_to']	= '{field} må inneholde et tall mindre enn eller lik {param}.';
$lang['form_validation_greater_than']		= '{field} må inneholde et tall større enn {param}.';
$lang['form_validation_greater_than_equal_to']	= '{field} må inneholde et tall større enn eller lik {param}.';
$lang['form_validation_error_message_not_set']	= 'Finner ingen feilmelding som hører til feltet {field}';
$lang['form_validation_in_list']		= '{field} må være én av: {param}.';
