<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = 'ไม่สามารถค้นหาตัวแปรโพสต์ที่เรียก userfile';
$lang['upload_file_exceeds_limit'] = 'ไฟล์ที่อัปโหลดเกินขนาดสูงสุดที่อนุญาตในการตั้งค่า PHP ของคุณ';
$lang['upload_file_exceeds_form_limit'] = 'ไฟล์ที่อัปโหลดเกินขนาดสูงสุดได้รับอนุญาตโดยการส่งจากแบบฟอร์ม';
$lang['upload_file_partial'] = 'ไฟล์ถูกอัพโหลดเพียงบางส่วนเท่านั้น';
$lang['upload_no_temp_directory'] = 'โฟลเดอร์ชั่วคราวหายไป';
$lang['upload_unable_to_write_file'] = 'ไฟล์ไม่สามารถเขียนไปยังดิสก์';
$lang['upload_stopped_by_extension'] = 'อัปโหลดไฟล์ถูกหยุดไว้โดย extension.';
$lang['upload_no_file_selected'] = 'คุณไม่ได้เลือกไฟล์ที่จะอัปโหลด';
$lang['upload_invalid_filetype'] = 'ประเภทของไฟล์ที่คุณกำลังพยายามที่จะอัปโหลดนั้นไม่ได้รับอนุญาต';
$lang['upload_invalid_filesize'] = 'ไฟล์ที่คุณกำลังพยายามที่จะอัปโหลดมีขนาดใหญ่กว่าขนาดที่ได้รับอนุญาต';
$lang['upload_invalid_dimensions'] = 'ภาพที่คุณกำลังพยายามที่จะอัปโหลดไม่พอดีกับขนาดที่ได้รับอนุญาต';
$lang['upload_destination_error'] = 'พบปัญหาขณะกำลังพยายามที่จะย้ายไฟล์ที่อัปโหลดไปยังปลายทาง';
$lang['upload_no_filepath'] = 'ตำแหน่งไฟล์ที่จะอัพโหลดไม่ถูกต้อง';
$lang['upload_no_file_types'] = 'คุณไม่ได้ระบุชนิดของไฟล์ที่ได้รับอนุญาตใดๆ';
$lang['upload_bad_filename'] = 'ชื่อไฟล์ที่คุณส่งมานั้นมีอยู่แล้วบนเซอร์เวอร์';
$lang['upload_not_writable'] = 'โฟลเดอร์ปลายทางอัพโหลดไม่สามารถเขียนได้';
