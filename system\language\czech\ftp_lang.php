<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ftp_no_connection']       = 'Nebylo možno nalézt ID spojení. Ujistěte se prosím, že jste navázali spojení před prací se soubory.';
$lang['ftp_unable_to_connect']   = 'Nebylo možné se připojit s daným hostname.';
$lang['ftp_unable_to_login']     = 'Nepodařilo se spojit s FTP serverem, zkontrolujte prosím přihlašovací jméno a heslo.';
$lang['ftp_unable_to_mkdir']     = 'Nepodařilo se vytvořit adresář.';
$lang['ftp_unable_to_changedir'] = 'Nepodařilo se změnit adresář.';
$lang['ftp_unable_to_chmod']     = 'Nepodařilo se nastavit práva souboru. Zkontrolujte prosím cestu.';
$lang['ftp_unable_to_upload']    = 'Nepodařilo se nahrát soubor. Zkontrolujte prosím cestu.';
$lang['ftp_unable_to_download']  = 'Nepodařilo se stáhnout soubor. Zkontrolujte prosím cestu.';
$lang['ftp_no_source_file']      = 'Nepodařilo se najít zdrojové soubory. Zkontrolujte prosím cestu.';
$lang['ftp_unable_to_rename']    = 'Nepodařilo se přejmenovat soubor.';
$lang['ftp_unable_to_delete']    = 'Nepodařilo se smazat soubor.';
$lang['ftp_unable_to_move']      = 'Nepodařilo se přesunout soubor. Zkontrolujte prosím, zda cílová složka existuje.';
