<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\BeyondCorp;

class Config extends \Google\Collection
{
  protected $collection_key = 'destinationRoutes';
  protected $destinationRoutesType = DestinationRoute::class;
  protected $destinationRoutesDataType = 'array';
  /**
   * @var string
   */
  public $transportProtocol;

  /**
   * @param DestinationRoute[]
   */
  public function setDestinationRoutes($destinationRoutes)
  {
    $this->destinationRoutes = $destinationRoutes;
  }
  /**
   * @return DestinationRoute[]
   */
  public function getDestinationRoutes()
  {
    return $this->destinationRoutes;
  }
  /**
   * @param string
   */
  public function setTransportProtocol($transportProtocol)
  {
    $this->transportProtocol = $transportProtocol;
  }
  /**
   * @return string
   */
  public function getTransportProtocol()
  {
    return $this->transportProtocol;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Config::class, 'Google_Service_BeyondCorp_Config');
