<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Video_conference_settings_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function get($platform = null)
    {
        $this->db->select('*');
        $this->db->from('video_conference_settings');
        
        if ($platform != null) {
            $this->db->where('platform', $platform);
        }
        
        $query = $this->db->get();
        if ($platform != null) {
            return $query->row();
        } else {
            return $query->result();
        }
    }

    public function getActive()
    {
        $this->db->select('*');
        $this->db->from('video_conference_settings');
        $this->db->where('is_active', 1);
        $query = $this->db->get();
        return $query->result();
    }

    public function add($data)
    {
        $this->db->trans_start();
        $this->db->insert('video_conference_settings', $data);
        $insert_id = $this->db->insert_id();
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === FALSE) {
            return false;
        }
        return $insert_id;
    }

    public function update($platform, $data)
    {
        $this->db->where('platform', $platform);
        $this->db->update('video_conference_settings', $data);
        return $this->db->affected_rows();
    }

    public function updateOrInsert($platform, $data)
    {
        $existing = $this->get($platform);
        
        if ($existing) {
            return $this->update($platform, $data);
        } else {
            $data['platform'] = $platform;
            return $this->add($data);
        }
    }

    public function delete($platform)
    {
        $this->db->where('platform', $platform);
        $this->db->delete('video_conference_settings');
        return $this->db->affected_rows();
    }

    public function toggleStatus($platform, $status)
    {
        $data = array(
            'is_active' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('platform', $platform);
        $this->db->update('video_conference_settings', $data);
        return $this->db->affected_rows();
    }

    public function getZoomSettings()
    {
        return $this->get('zoom');
    }

    public function getGmeetSettings()
    {
        return $this->get('gmeet');
    }

    public function getJitsiSettings()
    {
        return $this->get('jitsi');
    }

    public function isConfigured($platform)
    {
        $settings = $this->get($platform);
        
        if (!$settings) {
            return false;
        }

        switch ($platform) {
            case 'zoom':
                return !empty($settings->api_key) && !empty($settings->api_secret);
            case 'gmeet':
                return !empty($settings->client_id) && !empty($settings->client_secret);
            case 'jitsi':
                return true; // Jitsi doesn't require API keys for basic functionality
            default:
                return false;
        }
    }

    public function getDefaultSettings($platform)
    {
        $defaults = array(
            'zoom' => array(
                'platform' => 'zoom',
                'default_duration' => 60,
                'auto_recording' => 0,
                'waiting_room' => 1,
                'join_before_host' => 0,
                'mute_participants' => 1,
                'is_active' => 0,
                'settings_json' => json_encode(array(
                    'host_video' => true,
                    'participant_video' => false,
                    'cn_meeting' => false,
                    'in_meeting' => false,
                    'join_before_host' => false,
                    'mute_upon_entry' => true,
                    'watermark' => false,
                    'use_pmi' => false,
                    'approval_type' => 2,
                    'audio' => 'both',
                    'auto_recording' => 'none'
                ))
            ),
            'gmeet' => array(
                'platform' => 'gmeet',
                'default_duration' => 60,
                'auto_recording' => 0,
                'waiting_room' => 0,
                'join_before_host' => 1,
                'mute_participants' => 0,
                'is_active' => 0,
                'settings_json' => json_encode(array(
                    'allowAttendeeToEnableCamera' => true,
                    'allowAttendeeToEnableMic' => true,
                    'enableAttendeeList' => true,
                    'enableChat' => true
                ))
            ),
            'jitsi' => array(
                'platform' => 'jitsi',
                'default_duration' => 60,
                'auto_recording' => 0,
                'waiting_room' => 0,
                'join_before_host' => 1,
                'mute_participants' => 0,
                'is_active' => 1,
                'settings_json' => json_encode(array(
                    'startWithAudioMuted' => true,
                    'startWithVideoMuted' => false,
                    'enableWelcomePage' => false,
                    'enableUserRolesBasedOnToken' => false,
                    'enableLayerSuspension' => true,
                    'channelLastN' => -1,
                    'enableNoisyMicDetection' => true
                ))
            )
        );

        return isset($defaults[$platform]) ? $defaults[$platform] : array();
    }

    public function initializeDefaults()
    {
        $platforms = array('zoom', 'gmeet', 'jitsi');
        
        foreach ($platforms as $platform) {
            $existing = $this->get($platform);
            if (!$existing) {
                $defaults = $this->getDefaultSettings($platform);
                $this->add($defaults);
            }
        }
    }

    public function validateSettings($platform, $data)
    {
        $errors = array();

        switch ($platform) {
            case 'zoom':
                if (empty($data['api_key'])) {
                    $errors[] = 'Zoom API Key is required';
                }
                if (empty($data['api_secret'])) {
                    $errors[] = 'Zoom API Secret is required';
                }
                break;
            
            case 'gmeet':
                if (empty($data['client_id'])) {
                    $errors[] = 'Google Client ID is required';
                }
                if (empty($data['client_secret'])) {
                    $errors[] = 'Google Client Secret is required';
                }
                break;
            
            case 'jitsi':
                // Jitsi doesn't require API credentials for basic functionality
                break;
        }

        return $errors;
    }

    public function encryptSensitiveData($data)
    {
        $sensitive_fields = array('api_key', 'api_secret', 'client_secret', 'jwt_token');
        
        foreach ($sensitive_fields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                $data[$field] = $this->encryption->encrypt($data[$field]);
            }
        }
        
        return $data;
    }

    public function decryptSensitiveData($data)
    {
        $sensitive_fields = array('api_key', 'api_secret', 'client_secret', 'jwt_token');
        
        if (is_object($data)) {
            foreach ($sensitive_fields as $field) {
                if (isset($data->$field) && !empty($data->$field)) {
                    $data->$field = $this->encryption->decrypt($data->$field);
                }
            }
        } elseif (is_array($data)) {
            foreach ($sensitive_fields as $field) {
                if (isset($data[$field]) && !empty($data[$field])) {
                    $data[$field] = $this->encryption->decrypt($data[$field]);
                }
            }
        }
        
        return $data;
    }
}
