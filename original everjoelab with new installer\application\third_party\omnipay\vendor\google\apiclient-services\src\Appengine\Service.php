<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Appengine;

class Service extends \Google\Model
{
  /**
   * @var string
   */
  public $id;
  /**
   * @var string[]
   */
  public $labels;
  /**
   * @var string
   */
  public $name;
  protected $networkSettingsType = NetworkSettings::class;
  protected $networkSettingsDataType = '';
  protected $splitType = TrafficSplit::class;
  protected $splitDataType = '';

  /**
   * @param string
   */
  public function setId($id)
  {
    $this->id = $id;
  }
  /**
   * @return string
   */
  public function getId()
  {
    return $this->id;
  }
  /**
   * @param string[]
   */
  public function setLabels($labels)
  {
    $this->labels = $labels;
  }
  /**
   * @return string[]
   */
  public function getLabels()
  {
    return $this->labels;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param NetworkSettings
   */
  public function setNetworkSettings(NetworkSettings $networkSettings)
  {
    $this->networkSettings = $networkSettings;
  }
  /**
   * @return NetworkSettings
   */
  public function getNetworkSettings()
  {
    return $this->networkSettings;
  }
  /**
   * @param TrafficSplit
   */
  public function setSplit(TrafficSplit $split)
  {
    $this->split = $split;
  }
  /**
   * @return TrafficSplit
   */
  public function getSplit()
  {
    return $this->split;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Service::class, 'Google_Service_Appengine_Service');
