<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license   http://opensource.org/licenses/MIT MIT License
 * @link      https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']              = 'Pole {field} je povinné.';
$lang['form_validation_isset']                 = 'Pole {field} musí obsahovať hodnotu.';
$lang['form_validation_valid_email']           = 'Pole {field} musí obsahovať platnú emailovú adresu.';
$lang['form_validation_valid_emails']          = 'Pole {field} musí obsahovať platné emailové adresy.';
$lang['form_validation_valid_url']             = 'Pole {field} musí obsahovať platnú URL.';
$lang['form_validation_valid_ip']              = 'Pole {field} musí obsahovať platnú IP.';
$lang['form_validation_min_length']            = 'Pole {field} musí obsahovať aspoň {param} znakov.';
$lang['form_validation_max_length']            = 'Pole {field} nesmie obsahovať viac než {param} znakov.';
$lang['form_validation_exact_length']          = 'Pole {field} musí obsahovať presne {param} znakov.';
$lang['form_validation_alpha']                 = 'Pole {field} môže obsahovať iba znaky abecedy.';
$lang['form_validation_alpha_numeric']         = 'Pole {field} môže obsahovať iba znaky abecedy a čísla.';
$lang['form_validation_alpha_numeric_spaces']  = 'Pole {field} môže obsahovať iba znaky abecedy, čísla a mezery.';
$lang['form_validation_alpha_dash']            = 'Pole {field} môže obsahovať iba znaky abecedy, čísla, podtržítka a pomlčky.';
$lang['form_validation_numeric']               = 'Pole {field} môže obsahovať iba čísla.';
$lang['form_validation_is_numeric']            = 'Pole {field} môže obsahovať iba číselné znaky.';
$lang['form_validation_integer']               = 'Pole {field} musí být celé číslo.';
$lang['form_validation_regex_match']           = 'Pole {field} nie je v správnom formáte.';
$lang['form_validation_matches']               = 'Pole {field} nie je zhodné s poľom {param}.';
$lang['form_validation_differs']               = 'Pole {field} musí být rôzne od poľa {param}.';
$lang['form_validation_is_unique']             = 'Pole {field} musí obsahovať unikátnu hodnotu.';
$lang['form_validation_is_natural']            = 'Pole {field} môže obsahovať iba prirodzené čísla a nulu.';
$lang['form_validation_is_natural_no_zero']    = 'Pole {field} môže obsahovať iba prirodzené čísla.';
$lang['form_validation_decimal']               = 'Pole {field} musí obsahovať desatinné čislo.';
$lang['form_validation_less_than']             = 'Pole {field} musí být menšie než pole {param}.';
$lang['form_validation_less_than_equal_to']    = 'Pole {field} musí být menšie alebo rovnaké ako pole {param}.';
$lang['form_validation_greater_than']          = 'Pole {field} musí být väčšie než pole {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Pole {field} musí být väčšie alebo rovnaké ako pole {param}.';
$lang['form_validation_error_message_not_set'] = 'Pre pole {field} nie je nastavená chybová hláška.';
$lang['form_validation_in_list']               = 'Pole {field} musí být jedným z: {param}.'; 
