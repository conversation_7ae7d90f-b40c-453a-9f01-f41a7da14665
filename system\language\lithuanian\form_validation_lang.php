<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Laukas {field} yra privalomas.';
$lang['form_validation_isset']			= 'Laukas {field} turi turėti reikšmę.';
$lang['form_validation_valid_email']		= 'Lauke {field} turi būti teisingas el. pašto adresas.';
$lang['form_validation_valid_emails']		= 'Lauke {field} visi el. pašto adresai turi būti teisingi.';
$lang['form_validation_valid_url']		= 'Lauke {field} turi būti galiojantis URL adresas.';
$lang['form_validation_valid_ip']		= 'Lauke {field} turi būti taisyklingas IP adresas.';
$lang['form_validation_min_length']		= 'Lauką {field} turi sudaryti bent {param} ženklai/-ų.';
$lang['form_validation_max_length']		= 'Laukas {field} negali būti ilgesnis, nei {param} ženklai/-ų.';
$lang['form_validation_exact_length']		= 'Lauke {field} turi būti lygiai {param} ženklai/-ų.';
$lang['form_validation_alpha']			= 'Lauke {field} gali būti tik abėcėlės raidės.';
$lang['form_validation_alpha_numeric']		= 'Lauke {field} gali būti tik raidės ir skaičiai.';
$lang['form_validation_alpha_numeric_spaces']	= 'Lauke {field} gali būti tik raidės, skaičiai ir tarpai.';
$lang['form_validation_alpha_dash']		= 'Lauke {field} gali būti tik raidės, skaičiai, brūkšneliai ir apatiniai brūkšneliai..';
$lang['form_validation_numeric']		= 'Lauke {field} gali būti tik skaičiai.';
$lang['form_validation_is_numeric']		= 'Lauke {field} gali būti tik skaičių simboliai.';
$lang['form_validation_integer']		= 'Lauke {field} gali būti tik sveikieji skaičiai.';
$lang['form_validation_regex_match']		= 'Laulas {field} yra neteisingo formato.';
$lang['form_validation_matches']		= 'Laukas {field} neatitinka {param} lauko.';
$lang['form_validation_differs']		= 'Laukas {field} turi skirtis nuo {param} lauko.';
$lang['form_validation_is_unique'] 		= 'Lauke {field} turi būti unikali reikšmė.';
$lang['form_validation_is_natural']		= 'Lauke {field} gali būti tik skaitmenys.';
$lang['form_validation_is_natural_no_zero']	= 'Lauke {field} gali būti tik skaitmenys, ir jo reikšmė turi būti didesnė nei nulis.';
$lang['form_validation_decimal']		= 'Lauke {field} turi būti dešimtainis skaičius.';
$lang['form_validation_less_than']		= 'Lauke {field} turi būti skaičius, mažesnis už {param}.';
$lang['form_validation_less_than_equal_to']	= 'Lauke {field} turi būti skaičius, mažesnis ar lygus {param}.';
$lang['form_validation_greater_than']		= 'Lauke {field} turi būti skaičius, didesnis nei {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Lauke {field} turi būti skaičius, didesnis ar lygus {param}.';
$lang['form_validation_error_message_not_set']	= 'Nepavyko pasiekti klaidos pranešimo, atitinkančio lauko {field} pavadinimą.';
$lang['form_validation_in_list']		= 'Lauko {field} reikšmė turi atitikti vieną iš: {param}.';
