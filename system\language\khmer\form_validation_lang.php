<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'ទំរង់​ {field} ត្រូវបានទាមទារ។';
$lang['form_validation_isset']			= 'ទំរង់​ {field} ត្រូវតែមានតម្លៃមួយ។';
$lang['form_validation_valid_email']	= 'ទំរង់​ {field} ត្រូវតែមានអាសយដ្ឋានអ៊ីមែលត្រឹមត្រូវ។';
$lang['form_validation_valid_emails']	= 'ទំរង់​ {field} ត្រូវតែមានអាសយដ្ឋានអ៊ីមែលត្រឹមត្រូវទាំងអស់។';
$lang['form_validation_valid_url']		= 'ទំរង់​ {field} ត្រូវតែមាន​ URL ត្រឹម​ត្រូវ។';
$lang['form_validation_valid_ip']		= 'ទំរង់​ {field} ត្រូវតែមាន​ IP ត្រឹម​ត្រូវ។';
$lang['form_validation_min_length']		= 'ទំរង់​ {field} ត្រូវតែមានប្រវែងតូចជាង {param} តួអក្សរ។';
$lang['form_validation_max_length']		= 'ទំរង់​ {field} ត្រូវតែមានប្រវែងមិនធំជាង {param} តួអក្សរ។';
$lang['form_validation_exact_length']	= 'ទំរង់​ {field} ត្រូវតែមានប្រវែង {param} តួអក្សរ។';
$lang['form_validation_alpha']			= 'ទំរង់​ {field} អាចមានតែតួអក្សរ។';
$lang['form_validation_alpha_numeric']	= 'ទំរង់​ {field} អាចមានតែតួអក្សរ និងលេខ។';
$lang['form_validation_alpha_numeric_spaces']	= 'ទំរង់​ {field} អាចមានតែតួអក្សរ​ លេខ និងសហសញ្ញាដកឃ្លា។';
$lang['form_validation_alpha_dash']		= 'ទំរង់​ {field} អាចមានតែតួអក្សរ លេខ និងសហសញ្ញាគូសពីក្រោម (​ _ ) និង ត្រេកណ្តាល​ ( - )';
$lang['form_validation_numeric']		= 'ទំរង់​ {field} ត្រូវតែមានលេខប៉ុណ្ណោះ។';
$lang['form_validation_is_numeric']		= 'ទំរង់​ {field} ត្រូវតែមានតួអក្សរជាលេខតែប៉ុណ្ណោះ។';
$lang['form_validation_integer']		= 'ទំរង់​ {field} ត្រូវតែជាចំនួនគត់។';
$lang['form_validation_regex_match']	= 'ទំរង់​ {field} គឺមានទ្រង់ទ្រាយមិនត្រឹមត្រូវ។';
$lang['form_validation_matches']		= 'ទំរង់​ {field} មិនដូចគ្នានឹងទំរង់ {param}។';
$lang['form_validation_differs']		= 'ទំរង់​ {field} ត្រូវតែខុសគ្នាពីទំរង់ {param}។';
$lang['form_validation_is_unique'] 		= 'ទំរង់​ {field} ត្រូវតែមានតម្លៃតែមួយគត់។';
$lang['form_validation_is_natural']		= 'ទំរង់​ {field} ត្រូវមានតែខ្ទង់លេខ។';
$lang['form_validation_is_natural_no_zero']	= 'ទំរង់​ {field}​ ត្រូវតែមានខ្ទង់លេខប៉ុណ្ណោះ និងត្រូវតែធំជាងសូន្យ។';
$lang['form_validation_decimal']		= 'ទំរង់​ {field} ត្រូវតែជាចំនួនទសភាគ។';
$lang['form_validation_less_than']		= 'ទំរង់​ {field} ត្រូវតែជាលេខ និងតិចជាង {param}។';
$lang['form_validation_less_than_equal_to']	= 'ទំរង់​ {field} ត្រូវតែជាលេខ និងតិចជាង ឬស្មើ {param}។';
$lang['form_validation_greater_than']		= 'ទំរង់​ {field} ត្រូវតែជាលេខ និងច្រើនជាង {param}។';
$lang['form_validation_greater_than_equal_to']	= 'ទំរង់​ {field} ត្រូវតែជាលេខ និងធំជាង ឬស្មើ {param}។';
$lang['form_validation_error_message_not_set']	= 'មិន​អាច​ស្វែង​រក​កំហុស​ដែល​ត្រូវ​នឹង​ឈ្មោះ​ទំរង់​ {field} នេះទេ។';
$lang['form_validation_in_list']		= 'ទំរង់​ {field} ត្រូវតែមាន​​ក្នុង​ចំណោម: {param} ទាំងអស់​នេះ។';