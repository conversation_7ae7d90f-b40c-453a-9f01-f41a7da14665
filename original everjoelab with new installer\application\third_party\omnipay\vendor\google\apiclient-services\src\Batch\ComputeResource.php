<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Batch;

class ComputeResource extends \Google\Model
{
  /**
   * @var string
   */
  public $bootDiskMib;
  /**
   * @var string
   */
  public $cpuMilli;
  /**
   * @var string
   */
  public $memoryMib;

  /**
   * @param string
   */
  public function setBootDiskMib($bootDiskMib)
  {
    $this->bootDiskMib = $bootDiskMib;
  }
  /**
   * @return string
   */
  public function getBootDiskMib()
  {
    return $this->bootDiskMib;
  }
  /**
   * @param string
   */
  public function setCpuMilli($cpuMilli)
  {
    $this->cpuMilli = $cpuMilli;
  }
  /**
   * @return string
   */
  public function getCpuMilli()
  {
    return $this->cpuMilli;
  }
  /**
   * @param string
   */
  public function setMemoryMib($memoryMib)
  {
    $this->memoryMib = $memoryMib;
  }
  /**
   * @return string
   */
  public function getMemoryMib()
  {
    return $this->memoryMib;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ComputeResource::class, 'Google_Service_Batch_ComputeResource');
