<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ftp_no_connection'] = 'Невозможно найти действительный идентификатор соединения. Пожалуйста убедитесь, что соединение установлено, прежде чем выполнять любые операции с файлами.';
$lang['ftp_unable_to_connect'] = 'Невозможно подключиться к FTP серверу, используя предоставленное имя хоста.';
$lang['ftp_unable_to_login'] = 'Невозможно подключиться к FTP серверу. Пожалуйста, проверьте имя пользователя и пароль.';
$lang['ftp_unable_to_mkdir'] = 'Невозможно создать каталог с указанным Вами именем.';
$lang['ftp_unable_to_changedir'] = 'Невозможно изменить каталог.';
$lang['ftp_unable_to_chmod'] = 'Невозможно установить права доступа к файлам. Пожалуйста, проверьте указанный Вами путь. Примечание: эта функция доступна только в PHP 5 и выше.';
$lang['ftp_unable_to_upload'] = 'Невозможно загрузить указанный файл. Пожалуйста, проверьте указанный Вами путь.';
$lang['ftp_unable_to_download'] = 'Невозможно скачать указанный файл. Пожалуйста, проверьте указанный Вами путь.';
$lang['ftp_no_source_file'] = 'Невозможно найти исходный файл. Пожалуйста, проверьте указанный Вами путь.';
$lang['ftp_unable_to_rename'] = 'Невозможно переименовать файл.';
$lang['ftp_unable_to_delete'] = 'Невозможно удалить файл.';
$lang['ftp_unable_to_move'] = 'Невозможно переместить файл. Пожалуйста, убедитесь что каталог назначения существует.';
