<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license http://opensource.org/licenses/MIT MIT License
 * @link https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['imglib_source_image_required'] = '원본 이미지를 지정하세요.';
$lang['imglib_gd_required'] = '서버에 GD 이미지 라이브러리가 지원되지 않습니다.';
$lang['imglib_gd_required_for_props'] = '서버에서 이미지를 처리하기 위하여 GD 라이브러리가 필요합니다.';
$lang['imglib_unsupported_imagecreate'] = '서버에서 이미지를 처리하기 위하여 GD 라이브러리가 필요합니다.';
$lang['imglib_gif_not_supported'] = 'GIF는 라이센스 문제로 지원되지 않을 수 있습니다. JPG나 PNG 형식을 이용하세요.';
$lang['imglib_jpg_not_supported'] = '서버에서 JPG 형식은 지원하지 않습니다.';
$lang['imglib_png_not_supported'] = '서버에서 PNG 형식은 지원하지 않습니다.';
$lang['imglib_jpg_or_png_required'] = '이미지 리사이징은 서버에서 JPEG 혹은 PNG 형식이 지원되어야 합니다.';
$lang['imglib_copy_error'] = '파일을 작성하는데 실패했습니다. 디렉토리 권한을 확인하세요.';
$lang['imglib_rotate_unsupported'] = '서버에서 이미지 회전은 지원하지 않습니다.';
$lang['imglib_libpath_invalid'] = '이미지 라이브러리에 대한 경로가 올바르지 않습니다. 환경설정에서 올바른 경로인지 확인하세요.';
$lang['imglib_image_process_failed'] = '이미지 처리에 실패했습니다. 서버가 해당 형식을 지원하는지 확인하고, 이미지 라이브러리의 경로가 올바른지 확인하세요.';
$lang['imglib_rotation_angle_required'] = '회전하려면 각도를 입력하세요.';
$lang['imglib_invalid_path'] = '이미지 경로가 올바르지 않습니다.';
$lang['imglib_invalid_image'] = 'The provided image is not valid.';
$lang['imglib_copy_failed'] = '이미지 복사에 실패했습니다.';
$lang['imglib_missing_font'] = '해당 폰트를 찾을 수 없습니다.';
$lang['imglib_save_failed'] = '이미지를 저장할 수 없습니다. 권한을 확인하거나 경로를 확인하세요.';
