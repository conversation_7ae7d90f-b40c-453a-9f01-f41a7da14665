<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'โปรดระบุ {field} ';
$lang['form_validation_isset']			= 'จำเป็นต้องระบุข้อมูล {field} ';
$lang['form_validation_valid_email']		= 'กรุณากรอก {field} ให้ถูกต้องตามรูปแบบ Email';
$lang['form_validation_valid_emails']		= 'กรุณากรอก {field} ให้ถูกต้องตามรูปแบบ Email ทั้งหมด';
$lang['form_validation_valid_url']		= 'กรุณากรอก {field} ให้ถูกต้องตามรูปแบบ URL';
$lang['form_validation_valid_ip']		= 'กรุณากรอก {field} ให้ถูกต้องตามรูปแบบ IP Address';
$lang['form_validation_min_length']		= 'ข้อมูล {field} ต้องมีความยาวอย่างน้อย  {param}  ตัวอักษร';
$lang['form_validation_max_length']		= 'ข้อมูล {field} ต้องมีความยาวไม่เกิน  {param}  ตัวอักษร';
$lang['form_validation_exact_length']		= 'ข้อมูล {field} ต้องมีความยาว  {param}  ตัวอักษรเท่านั้น';
$lang['form_validation_alpha']			= 'ข้อมูล {field} จะต้องประกอบด้วยตัวอักษรเท่านั้น';
$lang['form_validation_alpha_numeric']		= 'ข้อมูล {field} จะต้องประกอบด้วยตัวอักษรหรือตัวเลขเท่านั้น';
$lang['form_validation_alpha_numeric_spaces']	= 'ข้อมูล {field} จะต้องประกอบด้วยตัวอักษรตัวเลขและช่องว่างเท่านั้น';
$lang['form_validation_alpha_dash']		= 'ข้อมูล {field} จะประกอบด้วยตัวอักษรตัวเลขใต้และขีดกลางเท่านั้น';
$lang['form_validation_numeric']		= 'ข้อมูล {field} จะตัวประกอบด้วยตัวอักษรตัวเลขใต้และขีดกลางเท่านั้น';
$lang['form_validation_is_numeric']		= 'ข้อมูล {field} จะต้องประกอบด้วยตัวเลขเท่านั้น';
$lang['form_validation_integer']		= 'ข้อมูล {field} จะต้องประกอบด้วยตัวเลขจำนวนเต็มเท่านั้น';
$lang['form_validation_regex_match']		= 'รูปแบบของข้อมูล {field} ไม่ถูกต้อง';
$lang['form_validation_matches']		= 'ข้อมูล {field} ไม่ตรงกับข้อมูล {param} .';
$lang['form_validation_differs']		= 'ข้อมูล {field} จะต้องแตกต่างจากข้อมูล {param} ';
$lang['form_validation_is_unique'] 		= 'ข้อมูล {field} จะต้องเป็นค่าที่ซ้ำกับข้อมูลที่มีอยู่ในระบบ';
$lang['form_validation_is_natural']		= 'ข้อมูล {field} จะต้องเป็นตัวเลขเท่านั้น';
$lang['form_validation_is_natural_no_zero']	= 'ข้อมูล {field} จะต้องเป้นตัวเลขที่มากกว่า 0 ';
$lang['form_validation_decimal']		= 'ข้อมูล {field} จะต้องเป็นจำนวนทศนิยมเท่านั้น';
$lang['form_validation_less_than']		= 'ข้อมูล {field} จะต้องเป็นตัวเลขที่มีค่าน้อยกว่า {param} ';
$lang['form_validation_less_than_equal_to']	= 'ข้อมูล {field} จะต้องเป็นตัวเลขที่มีค่าไม่เกิน {param} ';
$lang['form_validation_greater_than']		= 'ข้อมูล {field} จะต้องเป็นตัวเลขที่มีค่ามากกว่า {param} ';
$lang['form_validation_greater_than_equal_to']	= 'ข้อมูล {field} จะต้องเป็นตัวเลขที่มีค่าตั้งแต่ {param} ขึ้นไป.';
$lang['form_validation_error_message_not_set']	= 'ไม่สามารถเข้าถึงข้อผิดพลาด ที่สอดคล้องกับ ชื่อเขตข้อมูล ของคุณ {field} .';
$lang['form_validation_in_list']		= 'ข้อมูล  {field}  จะต้องเป็นหนึ่งในรายการต่อไปนี้เท่านั้น:  {param} .';
