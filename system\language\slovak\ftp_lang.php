<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license   http://opensource.org/licenses/MIT MIT License
 * @link      https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ftp_no_connection']       = 'Nebolo nájdené platné ID spojenia. Uistite sa, že máte otvorené pripojenie predtým ako začnete akúkoľvek manipuláciu so súbormi.';
$lang['ftp_unable_to_connect']   = 'Nie je možné pripojiť sa k FTP serveru zo zadanou adresou.';
$lang['ftp_unable_to_login']     = 'Nie je možné pripojiť sa k FTP serveru. Skontrolujte zadané užívateľské meno a heslo.';
$lang['ftp_unable_to_mkdir']     = '<PERSON>žadovaný adresár nie je možné vytvoriť.';
$lang['ftp_unable_to_changedir'] = 'Adresáre nie je možné zmeniť.';
$lang['ftp_unable_to_chmod']     = 'Nie je možné nastaviť práva k súboru. Skontrolujte cestu. Poznámka: Táto funkcia je dostupná iba v PHP verzie 5 a vyšších.';
$lang['ftp_unable_to_upload']    = 'Požadovaný súbor nemožno nahrať. Skontrolujte cestu.';
$lang['ftp_unable_to_download']  = 'Nepodařilo se stáhnout soubor. Zkontrolujte prosím cestu.';
$lang['ftp_no_source_file']      = 'Zrojový súbor nebol nájdený. Skontrolujte cestu.';
$lang['ftp_unable_to_rename']    = 'Súbor sa nedá premenovať.';
$lang['ftp_unable_to_delete']    = 'Súbor sa nedá zmazať.';
$lang['ftp_unable_to_move']      = 'Súbor sa nedá presunúť. Uistite sa, že cieľový adresár existuje.';
