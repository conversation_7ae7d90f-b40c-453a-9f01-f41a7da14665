<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year']      = 'Rok';
$lang['date_years']     = 'Lata';
$lang['date_month']     = 'Miesiąc';
$lang['date_months']    = 'Miesiące';
$lang['date_week']      = 'Tydzień';
$lang['date_weeks']     = 'Tygodni';
$lang['date_day']       = 'Dzień';
$lang['date_days']      = 'Dni';
$lang['date_hour']      = 'Godzina';
$lang['date_hours']     = 'Godzin';
$lang['date_minute']    = 'Minuta';
$lang['date_minutes']   = 'Minut';
$lang['date_second']    = 'Sekunda';
$lang['date_seconds']   = 'Sekund';

$lang['UM12']   = '(UTC -12:00) Czas wysp Baker i Howland';
$lang['UM11']   = '(UTC -11:00) Czas Niue';
$lang['UM10']   = '(UTC -10:00) Czas hawajsko-aleucki, wyspy Cooka, Tahiti';
$lang['UM95']   = '(UTC -9:30) Czas wysp Markiz';
$lang['UM9']    = '(UTC -9:00) Czas alaskański, wyspy Gambiera';
$lang['UM8']    = '(UTC -8:00) Czas pacyficzny, wyspa Clippertona';
$lang['UM7']    = '(UTC -7:00) Czas górski';
$lang['UM6']    = '(UTC -6:00) Czas centralny';
$lang['UM5']    = '(UTC -5:00) Czas wschodni, czas zachodniokaraibski';
$lang['UM45']   = '(UTC -4:30) Czas wenezuelski';
$lang['UM4']    = '(UTC -4:00) Czas atlantycki, czas wschodniokaraibski';
$lang['UM35']   = '(UTC -3:30) Czas nowofundlandzki';
$lang['UM3']    = '(UTC -3:00) Argentyna, Brazylia, Francja, Gujana Francuska, Urugwaj';
$lang['UM2']    = '(UTC -2:00) Georgia Południowa i Sandwich Południowy';
$lang['UM1']    = '(UTC -1:00) Azory i Wyspy Zielonego Przylądka';
$lang['UTC']    = '(UTC) Czas uniwersalny, czas zachodnioeuropejski';
$lang['UP1']    = '(UTC +1:00) Czas środkowoeuropejski, czas zachodnioafrykański';
$lang['UP2']    = '(UTC +2:00) Czas środkowoafrykański, czas wschodnioeuropejski, czas Kaliningradu';
$lang['UP3']    = '(UTC +3:00) Czas moskiewski, czas wschodnioafrykański, czas arabski';
$lang['UP35']   = '(UTC +3:30) Czas irański';
$lang['UP4']    = '(UTC +4:00) Czas azerbejdżański, czas Samary';
$lang['UP45']   = '(UTC +4:30) Afganistan';
$lang['UP5']    = '(UTC +5:00) Czas pakistański, czas Jekaterynburga';
$lang['UP55']   = '(UTC +5:30) Czas indyjski, czas Sri Lanki';
$lang['UP575']  = '(UTC +5:45) Czas nepalski';
$lang['UP6']    = '(UTC +6:00) Czas Bangladeszu, czas Bhutanu, czas Omska';
$lang['UP65']   = '(UTC +6:30) Wyspy Kokosowe, Birma';
$lang['UP7']    = '(UTC +7:00) Czas Krasnojarska, Kambodża, Laos, Tajlandia, Wietnam';
$lang['UP8']    = '(UTC +8:00) Czas zachodnioaustralijski, czas Pekinu, czas Irkucka';
$lang['UP875']  = '(UTC +8:45) Czas środkowo-zachodnioaustralijski';
$lang['UP9']    = '(UTC +9:00) Czas japoński, czas koreański, czas Jakucka';
$lang['UP95']   = '(UTC +9:30) Czas środkowoaustralijski';
$lang['UP10']   = '(UTC +10:00) Czas wschodnioaustralijski, czas Władywostoku';
$lang['UP105']  = '(UTC +10:30) Lord Howe';
$lang['UP11']   = '(UTC +11:00) Czas sredniekołymska, wyspy Salomona, Vanuatu';
$lang['UP115']  = '(UTC +11:30) Wyspa Norfolk';
$lang['UP12']   = '(UTC +12:00) Fiji, Wyspy Gilberta, czas kamczacki, czas nowozelandzki';
$lang['UP1275'] = '(UTC +12:45) Czas wysp Chatham';
$lang['UP13']   = '(UTC +13:00) Czas Samoa, czas Feniks, Tonga';
$lang['UP14']   = '(UTC +14:00) Wyspy Line';
