<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']               = 'O campo {field} é obrigatório.';
$lang['form_validation_isset']                  = 'O campo {field} deve conter um valor.';
$lang['form_validation_valid_email']            = 'O campo {field} deve conter um email válido.';
$lang['form_validation_valid_emails']           = 'O campo {field} deve conter todos os emails válidos.';
$lang['form_validation_valid_url']              = 'O campo {field} deve conter um URL válido.';
$lang['form_validation_valid_ip']               = 'O campo {field} deve conter um IP válido.';
$lang['form_validation_valid_mac']              = 'O campo {field} deve conter um endereço MAC válido.';
$lang['form_validation_min_length']             = 'O campo {field} deve conter pelo menos {param} caracteres.';
$lang['form_validation_max_length']             = 'O campo {field} não pode conter mais que {param} caracteres.';
$lang['form_validation_exact_length']           = 'O campo {field} deve conter exatamente {param} caracteres.';
$lang['form_validation_alpha']                  = 'O campo {field} só pode conter caracteres do alfabeto.';
$lang['form_validation_alpha_numeric']          = 'O campo {field} só pode conter caracteres alfanuméricos.';
$lang['form_validation_alpha_numeric_spaces']   = 'O campo {field} só pode conter caracteres alfanuméricos e espaços.';
$lang['form_validation_alpha_dash']             = 'O campo {field} só pode conter caracteres alfanuméricos, underscores (_) e hífenes (-).';
$lang['form_validation_numeric']                = 'O campo {field} só pode conter números.';
$lang['form_validation_is_numeric']             = 'O campo {field} só pode conter caracteres numéricos.';
$lang['form_validation_integer']                = 'O campo {field} só pode conter um número inteiro.';
$lang['form_validation_regex_match']            = 'O campo {field} não está no formato correcto.';
$lang['form_validation_matches']                = 'O campo {field} não coincide com o campo {param}.';
$lang['form_validation_differs']                = 'O campo {field} deve ser diferente do campo {param}.';
$lang['form_validation_is_unique']              = 'O campo {field} deve conter um valor único.';
$lang['form_validation_is_natural']             = 'O campo {field} deve conter um número natural.';
$lang['form_validation_is_natural_no_zero']     = 'O campo {field} deve conter um número inteiro positivo maior que zero.';
$lang['form_validation_decimal']                = 'O campo {field} deve conter um número decimal.';
$lang['form_validation_less_than']              = 'O campo {field} deve conter um número menor que {param}.';
$lang['form_validation_less_than_equal_to']     = 'O campo {field} deve conter um número menor ou igual que {param}.';
$lang['form_validation_greater_than']           = 'O campo {field} deve conter um número maior que {param}.';
$lang['form_validation_greater_than_equal_to']  = 'O campo {field} deve conter um número maior ou igual que {param}.';
$lang['form_validation_error_message_not_set']  = 'Não existe uma mensagem de erro para o campo {field}.';
$lang['form_validation_in_list']                = 'O campo {field} deve conter um dos valores: {param}.';
