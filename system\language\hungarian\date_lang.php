<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license		http://opensource.org/licenses/MIT	MIT License
 * @link		https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year']		= 'Év';
$lang['date_years']		= 'év';
$lang['date_month']		= 'Hónap';
$lang['date_months']	= 'hónap';
$lang['date_week']		= 'Hét';
$lang['date_weeks']		= 'hét';
$lang['date_day']		= 'Nap';
$lang['date_days']		= 'nap';
$lang['date_hour']		= 'Óra';
$lang['date_hours']		= 'óra';
$lang['date_minute']	= 'Perc';
$lang['date_minutes']	= 'perc';
$lang['date_second']	= 'Másodperc';
$lang['date_seconds']	= 'mp';

$lang['UM12']	= '(UTC -12:00) Baker-, Howland-sziget';
$lang['UM11']	= '(UTC -11:00) Szamoai Zónaidő, Niue';
$lang['UM10']	= '(UTC -10:00) Hawaii-Aleut Zónaidő, Cook-szigetek, Tahiti';
$lang['UM95']	= '(UTC -9:30) Marquesas-szigetek';
$lang['UM9']	= '(UTC -9:00) Alaszka Zónaidő, Gambier-szigetek';
$lang['UM8']	= '(UTC -8:00) Csendesóceáni Zónaidő, Clipperton-sziget';
$lang['UM7']	= '(UTC -7:00) Hegyvidéki Zónaidő (USA)';
$lang['UM6']	= '(UTC -6:00) Központi Zónaidő (USA)';
$lang['UM5']	= '(UTC -5:00) Keleti Zónaidő (USA), Nyugat-Karibi Zónaidő';
$lang['UM45']	= '(UTC -4:30) Venezuelai Zónaidő';
$lang['UM4']	= '(UTC -4:00) Atlanti Zónaidő (USA), Kelet-Karibi Zónaidő';
$lang['UM35']	= '(UTC -3:30) Újfundlandi Zónaidő';
$lang['UM3']	= '(UTC -3:00) Argentína, Brazilia, Francia Guyana, Uruguay';
$lang['UM2']	= '(UTC -2:00) Dél-Georgia,Dél-Sandwich-szigetek';
$lang['UM1']	= '(UTC -1:00) Azori-, Zöld-foki-szigetek';
$lang['UTC']	= '(UTC) Greenwich Világidő, Nyugat-Európai Idő';
$lang['UP1']	= '(UTC +1:00) Közép-Európai idő, Nyugat-Afrikai Idő';
$lang['UP2']	= '(UTC +2:00) Közép-Afrikai Idő, Kelet-Európai Idő, Kalinyingrádi Idő';
$lang['UP3']	= '(UTC +3:00) Moszkvai Idő, Kelet-Afrikai Idő';
$lang['UP35']	= '(UTC +3:30) Iráni Zónaidő';
$lang['UP4']	= '(UTC +4:00) Azerbajdzsáni Zónaidő, Szamarai Idő';
$lang['UP45']	= '(UTC +4:30) Afganisztán';
$lang['UP5']	= '(UTC +5:00) Pakisztáni Zónaidő, Jekatyerinburgi Idő';
$lang['UP55']	= '(UTC +5:30) Indiai Zónaidő, Srí Lankai Idő';
$lang['UP575']	= '(UTC +5:45) Nepáli Time';
$lang['UP6']	= '(UTC +6:00) Bangladesi Zónaidő, Bhutáni Idő, Omszki Idő';
$lang['UP65']	= '(UTC +6:30) Kókusz-szigetek, Mianmar';
$lang['UP7']	= '(UTC +7:00) Krasznojarszki Idő, Kambodzsa, Laosz, Thaiföld, Vietnam';
$lang['UP8']	= '(UTC +8:00) Ausztráliai Nyugati Zónaidő, Peking Idő, Irkutszk Idő';
$lang['UP875']	= '(UTC +8:45) Ausztráliai Nyugat-központi Zónaidő';
$lang['UP9']	= '(UTC +9:00) Japán Zónaidő, Koreai Zónaidő, Jakutszki Idő';
$lang['UP95']	= '(UTC +9:30) Ausztráliai Központi Zónaidő';
$lang['UP10']	= '(UTC +10:00) Ausztráliai Keleti Zónaidő, Vlagyivosztoki Idő';
$lang['UP105']	= '(UTC +10:30) Lord Howe-sziget';
$lang['UP11']	= '(UTC +11:00) Magadani Idő, Salamon-szigetek, Vanuatu';
$lang['UP115']	= '(UTC +11:30) Norfolk-sziget';
$lang['UP12']	= '(UTC +12:00) Fidzsi-, Gilbert-szigetek, Kamcsatkai Idő, Új-Zélandi Zónaidő';
$lang['UP1275']	= '(UTC +12:45) Chatham-szigeteki Zónaidő';
$lang['UP13']	= '(UTC +13:00) Szamoai Zónaidő, Főnix-szigeteki Idő, Tonga';
$lang['UP14']	= '(UTC +14:00) Line-szigetek';