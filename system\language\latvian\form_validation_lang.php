<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Laukam {field} jābūt obligātam.';
$lang['form_validation_isset']			= 'Lauks {field} nedrīkst būt tukšs.';
$lang['form_validation_valid_email']		= 'Laukam {field} jāsatur derīgu epasta adresi.';
$lang['form_validation_valid_emails']		= 'Laukā {field} visiem epastiem jābūt derīgiem.';
$lang['form_validation_valid_url']		= 'Laukam {field} jāsatur derīgu URL.';
$lang['form_validation_valid_ip']		= 'Laukam {field} jāsatur derīgu IP.';
$lang['form_validation_min_length']		= 'Laukam {field} jābūt vismaz {param} rakstzīmes garam.';
$lang['form_validation_max_length']		= 'Lauka {field} garums nedrīkst pārsniegt {param} rakstzīmes.';
$lang['form_validation_exact_length']		= 'Laukam {field} jābūt tieši {param} rakstzīmes garam.';
$lang['form_validation_alpha']			= 'Lauks {field} drīkst saturēt tikai burtu rakstzīmes.';
$lang['form_validation_alpha_numeric']		= 'Lauks {field} drīkst saturēt tikai burtu-ciparu rakstzīmes.';
$lang['form_validation_alpha_numeric_spaces']	= 'Lauks {field} drīkst saturēt tikai burtu-ciparu rakstzīmes un atstarpes.';
$lang['form_validation_alpha_dash']		= 'Lauks {field} drīkst saturēt tikai burtu-ciparu rakstzīmes, svītras un apakšsvītras.';
$lang['form_validation_numeric']		= 'Lauks {field} drīkst saturēt tikai skaitļus.';
$lang['form_validation_is_numeric']		= 'Lauks {field} drīkst saturēt tikai ciparu rakstzīmes.';
$lang['form_validation_integer']		= 'Lauks {field} drīkst saturēt tikai veselus skaitļus.';
$lang['form_validation_regex_match']		= 'Lauks {field} nav pareizā formātā.';
$lang['form_validation_matches']		= 'Lauks {field} nesakrīt ar {param} lauku.';
$lang['form_validation_differs']		= 'Laukam {field} jābūt atšķirīgam no {param} lauka.';
$lang['form_validation_is_unique'] 		= 'Laukam {field} jābūt ar unikālu vērtību.';
$lang['form_validation_is_natural']		= 'Laukā {field} drīkst būt tikai skaits.';
$lang['form_validation_is_natural_no_zero']	= 'Laukā {field} drīkst būt tikai skaits un kurš ir lielāks par nulli.';
$lang['form_validation_decimal']		= 'Laukā {field} jābūt decimālskaitlim.';
$lang['form_validation_less_than']		= 'Laukā {field} jābūt skaitlim mazākam par {param}.';
$lang['form_validation_less_than_equal_to']	= 'Laukā {field} jābūt skaitlim mazākam vai vienādam ar {param}.';
$lang['form_validation_greater_than']		= 'Laukā {field} jābūt skaitlim lielākam par {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Laukā {field} jābūt skaitlim lielākam vai vienādam ar {param}.';
$lang['form_validation_error_message_not_set']	= 'Nevar piekļūt atbilstošam kļūdas paziņojumam priekš {field} lauka.';
$lang['form_validation_in_list']		= 'Laukam {field} jābūt vienam no: {param}.';
