<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['profiler_database'] = '数据库';
$lang['profiler_controller_info'] = '类 / 方法';
$lang['profiler_benchmarks'] = '基准';
$lang['profiler_queries'] = '查询';
$lang['profiler_get_data'] = 'GET 数据';
$lang['profiler_post_data'] = 'POST 数据';
$lang['profiler_uri_string'] = 'URI 字符串';
$lang['profiler_memory_usage'] = '内存使用';
$lang['profiler_config'] = '设置值';
$lang['profiler_session_data'] = 'SESSION 数据';
$lang['profiler_headers'] = 'HTTP 头';
$lang['profiler_no_db'] = '当前没有已经载入的数据库驱动。  ';
$lang['profiler_no_queries'] = '运行，无查询。  ';
$lang['profiler_no_post'] = 'POST 数据不存在。';
$lang['profiler_no_get'] = 'GET 数据不存在。';
$lang['profiler_no_uri'] = 'URI 数据不存在。';
$lang['profiler_no_memory'] = '无法获得内存使用数据。';
$lang['profiler_no_profiles'] = '没有 Profile 数据 - 全部的 Profiler 都已被关闭。';
$lang['profiler_section_hide'] = '隐藏';
$lang['profiler_section_show'] = '显示';
$lang['profiler_seconds'] = '秒';
