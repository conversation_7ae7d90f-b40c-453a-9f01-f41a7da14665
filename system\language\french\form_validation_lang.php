<?php
/**
 * System messages translation for CodeIgniter(tm)
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']              = "Le champ {field} est requis.";
$lang['form_validation_isset']                 = "Le champ {field} doit avoir une valeur.";
$lang['form_validation_valid_email']           = "Le champ {field} doit contenir une adresse email valide.";
$lang['form_validation_valid_emails']          = "Le champ {field} ne peut contenir que des adresses email valides.";
$lang['form_validation_valid_url']             = "Le champ {field} doit contenir une URL valide.";
$lang['form_validation_valid_ip']              = "Le champ {field} doit contenir une IP valide.";
$lang['form_validation_min_length']            = "Le champ {field} doit contenir au moins {param} caractères.";
$lang['form_validation_max_length']            = "Le champ {field} ne peut contenir plus de {param} caractères.";
$lang['form_validation_exact_length']          = "Le champ {field} doit contenir exactement {param} caractères.";
$lang['form_validation_alpha']                 = "Le champ {field} ne peut contenir que des caractères alphabétiques.";
$lang['form_validation_alpha_numeric']         = "Le champ {field} ne peut contenir que des caractères alphanumériques.";
$lang['form_validation_alpha_numeric_spaces']  = "Le champ {field} ne peut contenir que des caractères alphanumériques et des espaces.";
$lang['form_validation_alpha_dash']            = "Le champ {field} ne peut contenir que des caractères alphanumériques, des caractères de soulignement et des traits d'union.";
$lang['form_validation_numeric']               = "Le champ {field} doit contenir un nombre (caractères numériques).";
$lang['form_validation_is_numeric']            = "Le champ {field} ne peut contenir que de signes du type nombre.";
$lang['form_validation_integer']               = "Le champ {field} doit contenir un nombre entier.";
$lang['form_validation_regex_match']           = "Le champ {field} n'utilise pas le bon format.";
$lang['form_validation_matches']               = "Le champ {field} doit correspondre au champ {param}.";
$lang['form_validation_differs']               = "Le champ {field} doit être différent du champ {param}.";
$lang['form_validation_is_unique']             = "Le champ {field} doit contenir une valeur unique.";
$lang['form_validation_is_natural']            = "Le champ {field} ne peut contenir que des nombres positifs.";
$lang['form_validation_is_natural_no_zero']    = "Le champ {field} ne peut contenir que des nombres plus grands que zéro.";
$lang['form_validation_decimal']               = "Le champ {field} doit contenir un nombre décimal.";
$lang['form_validation_less_than']             = "Le champ {field} doit contenir un nombre inférieur à {param}.";
$lang['form_validation_less_than_equal_to']    = "Le champ {field} doit contenir un nombre inférieur ou égal à {param}.";
$lang['form_validation_greater_than']          = "Le champ {field} doit contenir un nombre supérieur à {param}.";
$lang['form_validation_greater_than_equal_to'] = "Le champ {field} doit contenir un nombre supérieur ou égal à {param}.";
$lang['form_validation_error_message_not_set'] = "Impossible d'accéder à un message d'erreur correspondant à votre champ nommé {field}.";
$lang['form_validation_in_list']               = "Le champ {field} doit avoir une de ces valeurs : {param}.";
