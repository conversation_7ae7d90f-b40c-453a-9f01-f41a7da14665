<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license http://opensource.org/licenses/MIT MIT License
 * @link http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']				= '.فیلڈ کی ضرورت ہے {field}';
$lang['form_validation_isset']					= '.فیلڈ کی ویلیو لازمی ہے {field}';
$lang['form_validation_valid_email']			= '.فیلڈ کو ایک درست ای میل ایڈریس پر مشتمل ہونا چاہئے {field}';
$lang['form_validation_valid_emails']			= '.فیلڈ تمام درست ای میل پتوں پر مشتمل ہونا چاہئے {field}';
$lang['form_validation_valid_url']				= '.پر مشتمل ہونا چاہئے URL فیلڈ کو ایک درست {field}';
$lang['form_validation_valid_ip']				= '.پر مشتمل ہونا چاہئے IP فیلڈ ایک درست {field}';
$lang['form_validation_min_length']				= '.حروف کا ہونا ضروری ہے {param} فیلڈ کی لمبائی میں کم از کم {field}';
$lang['form_validation_max_length']				= '.حروف سے زیادہ نہیں ہو سکتی {param} فیلڈ کی لمبائی {field}';
$lang['form_validation_exact_length']			= '.حروف ہونا لازمی ہے {param} فیلڈ کی لمبائی بالکل {field}';
$lang['form_validation_alpha']					= '.فیلڈ صرف حروف تہجی پر مشتمل ہو {field}';
$lang['form_validation_alpha_numeric']			= '.فیلڈ صرف حروف اور عددی حروف پر مشتمل ہو {field}';
$lang['form_validation_alpha_numeric_spaces']	= '.فیلڈ صرف حروف، عددی حروف اور خالی جگہوں پر مشتمل ہو {field}';
$lang['form_validation_alpha_dash']				= '.فیلڈ صرف حروف، عددی حروف، انڈر سکور اور ڈیش پر  مشتمل ہو سکتا ہے {field}';
$lang['form_validation_numeric']				= '.فیلڈ صرف اعداد پر مشتمل ہونا چاہئے {field}';
$lang['form_validation_is_numeric']				= '.فیلڈ صرف عددی حروف پر مشتمل ہونا چاہئے {field}';
$lang['form_validation_integer']				= '.فیلڈ ایک عددی پر مشتمل ہونا چاہئے {field}';
$lang['form_validation_regex_match']			= '.فیلڈ درست شکل میں نہیں ہے {field}';
$lang['form_validation_matches']				= '.فیلڈ سے مماثل نہیں ہے {param} فیلڈ {field}';
$lang['form_validation_differs']				= '.فیلڈ سے مختلف ہونا چاہیے {param} فیلڈ {field}';
$lang['form_validation_is_unique'] 				= '.فیلڈ میں ایک منفرد قیمت کا مشتمل ہونا لازمی {field}';
$lang['form_validation_is_natural']				= '.فیلڈ کا صرف ہندسوں مشتمل ہونا لازمی ہے {field}';
$lang['form_validation_is_natural_no_zero']		= '.فیلڈ کا صرف ہندسوں مشتمل ہونا لازمی اور صفر سے زیادہ ہونا چاہیے {field}';
$lang['form_validation_decimal']				= '.تعداد پر مشتمل ہونا لازمی decimal فیلڈ کو ایک {field}';
$lang['form_validation_less_than']				= '.کے مقابلے میں ایک نمبر سے کم پر مشتمل ہونا چاہئے {param} فیلڈ کے {field}';
$lang['form_validation_less_than_equal_to']		= '.ایک بڑی تعداد سے کم یا برابر کا ہونا لازمی ہے {param} فیلڈ کے {field}';
$lang['form_validation_greater_than']			= '.کے مقابلے میں ایک نمبر بڑھ کر ہونا لازمی ہے {param} فیلڈ کا {field}';
$lang['form_validation_greater_than_equal_to']	= '.کے مقابلے میں تعداد سے زیادہ یا برابر ہونا لازمی ہے {param} فیلڈ کا {field}';
$lang['form_validation_error_message_not_set']	= '.سے مطابقت رکھتا ہوا ایرر میسج نہیں مل سکا {field} آپکی دی ہوئی فیلڈ';
$lang['form_validation_in_list']				= '{param}: فیلڈ کو ان میں سے کسی ایک پر مشتمل ہونا چاہیے {field}';
