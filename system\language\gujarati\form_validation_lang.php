<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'આ {field} ક્ષેત્ર જરૂરી છે.';
$lang['form_validation_isset']			= '{field} ક્ષેત્રમાં એક મૂલ્ય હોવી જ જોઈએ.';
$lang['form_validation_valid_email']		= '{field} ક્ષેત્રમાં એક માન્ય ઇમેઇલ સરનામું હોવુ જરૂરી છે.';
$lang['form_validation_valid_emails']		= '{field} ક્ષેત્રમાં બધી માન્ય ઇમેઇલ સરનામા હોવુ જરૂરી છે.';
$lang['form_validation_valid_url']		= '{field} ક્ષેત્રમાં માન્ય URL હોવુ જરૂરી છે.';
$lang['form_validation_valid_ip']		= '{field} ક્ષેત્રમાં માન્ય IP હોવુ જરૂરી છે.';
$lang['form_validation_min_length']		= '{field} ક્ષેત્રમાં લંબાઈ ઓછામાં ઓછા {param} અક્ષરો હોવા જોઈએ.';
$lang['form_validation_max_length']		= '{field} ક્ષેત્રમાં લંબાઈ {param} અક્ષરો કરતાં ના વધી શકે..';
$lang['form_validation_exact_length']		= '{field} ક્ષેત્રમાં લંબાઈ બરાબર {param} અક્ષરો જ હોવી જોઈએ..';
$lang['form_validation_alpha']			= '{field} ક્ષેત્ર માં માત્ર મૂળાક્ષર અક્ષરો સમાવી શકે.';
$lang['form_validation_alpha_numeric']		= '{field} ક્ષેત્ર માં માત્ર આલ્ફાન્યૂમેરિક અક્ષરો સમાવી શકે.';
$lang['form_validation_alpha_numeric_spaces']	= '{field} ક્ષેત્ર માત્ર આલ્ફાન્યૂમેરિક અક્ષરો અને જગ્યાઓ સમાવી શકે.';
$lang['form_validation_alpha_dash']		= '{field}  ક્ષેત્ર માં માત્ર આલ્ફાન્યૂમેરિક અક્ષરો, અન્ડરસ્કૉરસ, અને ડેશો સમાવી શકે.';
$lang['form_validation_numeric']		= '{field} ક્ષેત્ર માં માત્ર નંબરો હોવા જરૂરી છે.';
$lang['form_validation_is_numeric']		= '{field} ક્ષેત્ર માં ફક્ત આંકડાકીય અક્ષરો સમાવી શકે.';
$lang['form_validation_integer']		= '{field} ક્ષેત્ર માં પૂર્ણાંક હોવા જરૂરી છે.';
$lang['form_validation_regex_match']		= '{field} ક્ષેત્ર યોગ્ય ફોર્મેટમાં નથી.';
$lang['form_validation_matches']		= '{field}  ક્ષેત્ર {param} ક્ષેત્ર ના બરાબર નથી.';
$lang['form_validation_differs']		= '{field} ક્ષેત્ર {param} ક્ષેત્ર થી અલગ જોઈએ.';
$lang['form_validation_is_unique'] 		= '{field} ક્ષેત્ર માં અનન્ય અથવા અલગ મૂલ્ય હોવી જરૂરી છે.';
$lang['form_validation_is_natural']		= '{field} ક્ષેત્ર માં ફક્ત અંકો હોવા જરૂરી છે.';
$lang['form_validation_is_natural_no_zero']	= '{field} ક્ષેત્ર માં ફક્ત અંકો હોવો જરૂરી છે અને શૂન્ય કરતાં મોટી મૂલ્ય હોવી જોઈએ.';
$lang['form_validation_decimal']		= '{field} ક્ષેત્ર માં દશાંશ નંબર હોવા જરૂરી છે.';
$lang['form_validation_less_than']		= '{field} ક્ષેત્ર માં {param} કરતાં એક નંબર ઓછો હોવો જરૂરી છે .';
$lang['form_validation_less_than_equal_to']	= '{field} ક્ષેત્ર માં {param} કરતા આંકડા નુ મુલ્ય ઓછુ અથવા સમાન હોવો જોઇ એ.';
$lang['form_validation_greater_than']		= '{field} ક્ષેત્ર માં {param} કરતાં આંકડા નુ મુલ્ય વધારે હોવુ જરૂરી છે.';
$lang['form_validation_greater_than_equal_to']	= '{field} ક્ષેત્ર માં {param} કરતા આંકડા નુ મુલ્ય વધારે અથવા સમાન હોવો જોઇ એ.';
$lang['form_validation_error_message_not_set']	= '{field} : તમારા આ ક્ષેત્ર નામ ના વીશે ભૂલ સંદેશો ઍક્સેસ કરવામાં નિષ્ફળતા .';
$lang['form_validation_in_list']		= '{field} ક્ષેત્ર {param} માં હોવુ જોઈએ.';
