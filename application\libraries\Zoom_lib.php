<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Zoom_lib
{
    protected $CI;
    protected $api_key;
    protected $api_secret;
    protected $base_url = 'https://api.zoom.us/v2/';
    protected $jwt_token;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('video_conference_settings_model');
        $this->loadSettings();
    }

    private function loadSettings()
    {
        $settings = $this->CI->video_conference_settings_model->getZoomSettings();
        if ($settings) {
            $settings = $this->CI->video_conference_settings_model->decryptSensitiveData($settings);
            $this->api_key = $settings->api_key;
            $this->api_secret = $settings->api_secret;
            $this->jwt_token = $settings->jwt_token;
        }
    }

    public function generateJWT()
    {
        if (empty($this->api_key) || empty($this->api_secret)) {
            return false;
        }

        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'iss' => $this->api_key,
            'exp' => time() + 3600 // Token expires in 1 hour
        ]);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->api_secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    private function makeRequest($endpoint, $method = 'GET', $data = null)
    {
        $jwt = $this->generateJWT();
        if (!$jwt) {
            return ['error' => 'Unable to generate JWT token. Check API credentials.'];
        }

        $curl = curl_init();
        $url = $this->base_url . $endpoint;

        $headers = [
            'Authorization: Bearer ' . $jwt,
            'Content-Type: application/json'
        ];

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
        ]);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return ['error' => 'cURL Error: ' . $err];
        }

        $decoded = json_decode($response, true);
        $decoded['http_code'] = $httpCode;

        return $decoded;
    }

    public function createMeeting($meetingData)
    {
        $defaultSettings = [
            'topic' => $meetingData['title'],
            'type' => 2, // Scheduled meeting
            'start_time' => date('c', strtotime($meetingData['start_time'])),
            'duration' => $meetingData['duration'],
            'timezone' => date_default_timezone_get(),
            'password' => $meetingData['password'] ?? $this->generatePassword(),
            'agenda' => $meetingData['description'] ?? '',
            'settings' => [
                'host_video' => true,
                'participant_video' => false,
                'cn_meeting' => false,
                'in_meeting' => false,
                'join_before_host' => false,
                'mute_upon_entry' => true,
                'watermark' => false,
                'use_pmi' => false,
                'approval_type' => 2,
                'audio' => 'both',
                'auto_recording' => 'none'
            ]
        ];

        $response = $this->makeRequest('users/me/meetings', 'POST', $defaultSettings);

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['id'])) {
            return [
                'success' => true,
                'meeting_id' => $response['id'],
                'meeting_url' => $response['join_url'],
                'password' => $response['password'],
                'host_url' => $response['start_url'],
                'response' => $response
            ];
        }

        return ['error' => 'Failed to create meeting', 'response' => $response];
    }

    public function updateMeeting($meetingId, $meetingData)
    {
        $updateData = [
            'topic' => $meetingData['title'],
            'start_time' => date('c', strtotime($meetingData['start_time'])),
            'duration' => $meetingData['duration'],
            'agenda' => $meetingData['description'] ?? ''
        ];

        $response = $this->makeRequest("meetings/{$meetingId}", 'PATCH', $updateData);

        if (isset($response['error'])) {
            return $response;
        }

        if ($response['http_code'] == 204) {
            return ['success' => true, 'message' => 'Meeting updated successfully'];
        }

        return ['error' => 'Failed to update meeting', 'response' => $response];
    }

    public function deleteMeeting($meetingId)
    {
        $response = $this->makeRequest("meetings/{$meetingId}", 'DELETE');

        if (isset($response['error'])) {
            return $response;
        }

        if ($response['http_code'] == 204) {
            return ['success' => true, 'message' => 'Meeting deleted successfully'];
        }

        return ['error' => 'Failed to delete meeting', 'response' => $response];
    }

    public function getMeeting($meetingId)
    {
        $response = $this->makeRequest("meetings/{$meetingId}");

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['id'])) {
            return [
                'success' => true,
                'meeting' => $response
            ];
        }

        return ['error' => 'Meeting not found', 'response' => $response];
    }

    public function getMeetingParticipants($meetingId)
    {
        $response = $this->makeRequest("meetings/{$meetingId}/participants");

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['participants'])) {
            return [
                'success' => true,
                'participants' => $response['participants']
            ];
        }

        return ['error' => 'No participants found', 'response' => $response];
    }

    public function listMeetings($userId = 'me', $type = 'scheduled')
    {
        $response = $this->makeRequest("users/{$userId}/meetings?type={$type}");

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['meetings'])) {
            return [
                'success' => true,
                'meetings' => $response['meetings']
            ];
        }

        return ['error' => 'No meetings found', 'response' => $response];
    }

    public function getRecordings($meetingId)
    {
        $response = $this->makeRequest("meetings/{$meetingId}/recordings");

        if (isset($response['error'])) {
            return $response;
        }

        if (isset($response['recording_files'])) {
            return [
                'success' => true,
                'recordings' => $response['recording_files']
            ];
        }

        return ['error' => 'No recordings found', 'response' => $response];
    }

    private function generatePassword($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $password;
    }

    public function testConnection()
    {
        $response = $this->makeRequest('users/me');

        if (isset($response['error'])) {
            return ['success' => false, 'message' => $response['error']];
        }

        if (isset($response['id'])) {
            return ['success' => true, 'message' => 'Connection successful', 'user' => $response];
        }

        return ['success' => false, 'message' => 'Connection failed', 'response' => $response];
    }

    public function isConfigured()
    {
        return !empty($this->api_key) && !empty($this->api_secret);
    }

    public function webhookHandler($payload)
    {
        // Handle Zoom webhooks for meeting events
        $event = $payload['event'] ?? '';
        $meeting = $payload['payload']['object'] ?? array();

        switch ($event) {
            case 'meeting.started':
                return $this->handleMeetingStarted($meeting);
            case 'meeting.ended':
                return $this->handleMeetingEnded($meeting);
            case 'meeting.participant_joined':
                return $this->handleParticipantJoined($meeting);
            case 'meeting.participant_left':
                return $this->handleParticipantLeft($meeting);
            default:
                return ['success' => false, 'message' => 'Unknown event type'];
        }
    }

    private function handleMeetingStarted($meeting)
    {
        // Update meeting status in database
        $this->CI->load->model('video_conference_model');
        $conference = $this->CI->video_conference_model->get();

        foreach ($conference as $conf) {
            if ($conf->meeting_id == $meeting['id']) {
                $this->CI->video_conference_model->updateStatus($conf->id, 'ongoing');
                break;
            }
        }

        return ['success' => true, 'message' => 'Meeting started'];
    }

    private function handleMeetingEnded($meeting)
    {
        // Update meeting status in database
        $this->CI->load->model('video_conference_model');
        $conference = $this->CI->video_conference_model->get();

        foreach ($conference as $conf) {
            if ($conf->meeting_id == $meeting['id']) {
                $this->CI->video_conference_model->updateStatus($conf->id, 'completed');
                break;
            }
        }

        return ['success' => true, 'message' => 'Meeting ended'];
    }

    private function handleParticipantJoined($meeting)
    {
        // Log participant join
        return ['success' => true, 'message' => 'Participant joined'];
    }

    private function handleParticipantLeft($meeting)
    {
        // Log participant leave
        return ['success' => true, 'message' => 'Participant left'];
    }
}
