<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2016, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	http://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = "Неможливо знайти змінну з ім’ям userfile в масиві POST.";
$lang['upload_file_exceeds_limit'] = "Розмір завантаженого файлу перевищує максимально допустимий розмір, вказаний у файлі конфігурації PHP.";
$lang['upload_file_exceeds_form_limit'] = "Розмір завантаженого файлу перевищує максимально допустимий розмір, вказаний у відправленій формі.";
$lang['upload_file_partial'] = "Завантажений файл був отриманий лише частково.";
$lang['upload_no_temp_directory'] = "Каталог збереження тимчасових файлів не існує.";
$lang['upload_unable_to_write_file'] = "Не вдалося записати файл на диск.";
$lang['upload_stopped_by_extension'] = "Розширення PHP зупинило завантаження файлу.";
$lang['upload_no_file_selected'] = "Необхідно выбрати файл для завантаження.";
$lang['upload_invalid_filetype'] = "Завантаження файлів цього типу заборонено.";
$lang['upload_invalid_filesize'] = "Розмір завантаженого файлу перевищує максимально допустимий.";
$lang['upload_invalid_dimensions'] = "Розміри завантаженого зображення перевищують максимально допустимі.";
$lang['upload_destination_error'] = "Неможливо перенести завантажений файл в каталог призначення.";
$lang['upload_no_filepath'] = "Некорректний каталог для завантаження.";
$lang['upload_no_file_types'] = "Необхідно вказати прийнятні для завантаження типи файлів.";
$lang['upload_bad_filename'] = "Файл із вказаним ім’ям уже існує на сервері.";
$lang['upload_not_writable'] = "Каталог для завантаження недоступний для запису.";
