<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['imglib_source_image_required'] = 'คุณต้องระบุแหล่งที่มาของภาพในการตั้งค่าของคุณ';
$lang['imglib_gd_required'] = 'จำเป็นต้องใช้ GD image สำหรับการทำงานนี้';
$lang['imglib_gd_required_for_props'] = 'เซิร์ฟเวอร์ของคุณต้องสนับสนุน GD image library เพื่อตรวจสอบคุณสมบัติของภาพ';
$lang['imglib_unsupported_imagecreate'] = 'เซิร์ฟเวอร์ของคุณไม่สนับสนุน GD function ซึ่งจำเป็นในการประมวลผลสำหรับภาพนชนิดนี้';
$lang['imglib_gif_not_supported'] = 'ภาพ GIF มักจะไม่ได้รับการสนับสนุนเนื่องจากข้อจำกัดของใบอนุญาต (license) คุณอาจจะต้องใช้ภาพ JPG หรือ PNG แทน';
$lang['imglib_jpg_not_supported'] = 'ไม่สนับสนุนภาพชนิด JPG';
$lang['imglib_png_not_supported'] = 'ไม่สนับสนุนภาพชนิด PNG';
$lang['imglib_jpg_or_png_required'] = 'วิธีการปรับขนาดภาพที่ระบุไว้ในการตัังค่าของสามารทำงานได้กับภาพประเภท JPEG หรือ PNG เท่านั้น';
$lang['imglib_copy_error'] = 'พบข้อผิดพลาดในขณะที่พยายามที่จะแทนที่แฟ้ม กรุณาตรวจสอบให้แน่ใจไดเรกทอรีไฟล์ของคุณสามารถเขียนได้';
$lang['imglib_rotate_unsupported'] = 'เซิร์ฟเวอร์ของคุณไม่สนับสนุนการหมุนภาพ';
$lang['imglib_libpath_invalid'] = 'Path ไปยัง image libraryของคุณไม่ถูกต้อง กรุณาตั้งค่าเส้นทางที่ถูกต้องในการตั้งค่าภาพของคุณ';
$lang['imglib_image_process_failed'] = 'ล้มเหลวในการประมวลผลภาพ กรุณาตรวจสอบว่าเซิร์ฟเวอร์ของคุณรองรับโปรโตคอลที่ใช้และเส้นทางไปยัง image library ของคุณถูกต้อง';
$lang['imglib_rotation_angle_required'] = 'โปรดระบุมุุมสำหรับการหมุนภาพ';
$lang['imglib_invalid_path'] = 'ตำแหน่งของภาพไม่ถูกต้อง';
$lang['imglib_invalid_image'] = 'ภาพที่ระบุไม่ถูกต้อง';
$lang['imglib_copy_failed'] = 'การคัดลอกภาพล้มเหลว';
$lang['imglib_missing_font'] = 'ไม่พบแบบอัษร]ต้องการ';
$lang['imglib_save_failed'] = 'ไม่สามารถบันทึกภาพได้กรุณาตรวจสอบภาพและไดเรกทอรี่ว่าสามารถเขียนได้หรือไม่';
