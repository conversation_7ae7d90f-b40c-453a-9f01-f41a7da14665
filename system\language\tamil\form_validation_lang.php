<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'இந்த  {field}  புலம் தேவை .';
$lang['form_validation_isset']			= '{field} புலம் ஒரு மதிப்பினை கொண்டிருக்க வேண்டும். ';
$lang['form_validation_valid_email']		= '{field} - புலம் சரியான மின்னஞ்சல் முகவரியை கொண்டிருக்க வேண்டும். ';
$lang['form_validation_valid_emails']		= '{field} புலம் அனைத்து சரியான மின்னஞ்சல் முகவரிகளை கொண்டிருக்க வேண்டும்.';
$lang['form_validation_valid_url']		= '{field} புலம் சரியான  URLஐ கொண்டிருக்க வேண்டும்.';
$lang['form_validation_valid_ip']		= '{field} புலம் சரியான  IPஐ கொண்டிருக்க வேண்டும்.';
$lang['form_validation_min_length']		= '{field} இந்த புலம்  குறைந்தது {param} எழுத்துகளை கொண்டிருக்க வேண்டும்..';
$lang['form_validation_max_length']		= '{field} இந்த புலம் {param} எழுத்துகளை  மிஞ்ச கூடாது. ';
$lang['form_validation_exact_length']		= '{field} புலம் சரியாக {param} எழுத்துகளை கொண்டிருக்க வேண்டும்..';
$lang['form_validation_alpha']			= '{field} புலம்  அகரவரிசை (alphabetical) எழுத்துகளை மட்டும் கொண்டிருக்கலாம்.';
$lang['form_validation_alpha_numeric']		= '{field} புலம்  எண்-எழுத்துகளை (alpha-numeric) மட்டும் கொண்டிருக்கலாம்.';
$lang['form_validation_alpha_numeric_spaces']	= '{field} புலம்  எண்-எழுத்துகள் (alpha-numeric)  மற்றும் இடைவெளிகளை மட்டும் கொண்டிருக்கலாம்.';
$lang['form_validation_alpha_dash']		= '{field} புலம் எண்-எழுத்துகள் (alpha-numeric) , அடிகோடுகள் மற்றும் சிறுகோடுகளை மட்டும் கொண்டிருக்கலாம். ';
$lang['form_validation_numeric']		= '{field} புலம்  எண்களை மட்டும் கொண்டிருக்க வேண்டும். ';
$lang['form_validation_is_numeric']		= '{field} புலம்  எண்ணுருக்களை மட்டும் கொண்டிருக்க வேண்டும். ';
$lang['form_validation_integer']		= '{field} புலம்  முழு எண்களை (Integers) மட்டும் கொண்டிருக்க வேண்டும். .';
$lang['form_validation_regex_match']		= '{field} புலம் சரியான வடிவமைப்பில் இல்லை.';
$lang['form_validation_matches']		= '{field} புலம் {param} என்ற புலத்தை ஒத்திருக்க வேண்டும். ';
$lang['form_validation_differs']		= '{field} புலம்  {param} என்ற புலத்திலுருந்து வேறுபட வேண்டும். ';
$lang['form_validation_is_unique'] 		= '{field} புலம் ஒரு தனிப்பட்ட மதிப்பை கொண்டிருக்க வேண்டும். ';
$lang['form_validation_is_natural']		= '{field} புலம் எண்ணிலக்கங்களை மட்டும் கொண்டிருக்க வேண்டும்..';
$lang['form_validation_is_natural_no_zero']	= '{field} புலம் எண்ணிலக்கங்களை மட்டும் கொண்டிருக்க வேண்டும் மற்றும் பூச்சியத்தைவிட அதிகமாக இருக்க வேண்டும்.';
$lang['form_validation_decimal']		= '{field} புலம் தசம எண்களை மட்டும் கொண்டிருக்க வேண்டும்.';
$lang['form_validation_less_than']		= '{field} புலம் {param}ஐ விட குறைவான எண் மதிப்பை கொண்டிருக்க வேண்டும்.';
$lang['form_validation_less_than_equal_to']	= '{field} புலம் {param}ஐ விட குறைவான  அல்லது சமமான எண் மதிப்பை கொண்டிருக்க வேண்டும். ';
$lang['form_validation_greater_than']		= '{field} புலம் {param}ஐ விட  பெரிய எண்ணாக இருக்க வேண்டும்.';
$lang['form_validation_greater_than_equal_to']	= '{field} புலம் {param}ஐ விட அதிகமான  அல்லது சமமான எண் மதிப்பை கொண்டிருக்க வேண்டும்.';
$lang['form_validation_error_message_not_set']	= '{field} என்ற புலத்திற்கான பிழை செய்தியை அனுக முடியவில்லை.';
$lang['form_validation_in_list']		= '{field} என்ற புலம்,  {param} - இவற்றில் ஒன்றாக இருக்க வேண்டும்.';
