<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license http://opensource.org/licenses/MIT MIT License
 * @link https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= '{field} är obligatoriskt.';
$lang['form_validation_isset']			= '{field} måste ha ett värde.';
$lang['form_validation_valid_email']		= '{field} måste innehålla en giltig e-postadress.';
$lang['form_validation_valid_emails']		= '{field} måste innehålla giltiga e-postadresser.';
$lang['form_validation_valid_url']		= '{field} måste innehålla en giltig webbadress.';
$lang['form_validation_valid_ip']		= '{field} måste innehålla en giltig IP-adress.';
$lang['form_validation_min_length']		= '{field} måste vara minst {param} tecken långt.';
$lang['form_validation_max_length']		= '{field} får inte vara mer än {param} tecken långt.';
$lang['form_validation_exact_length']		= '{field} måste vara exakt {param} tecken långt.';
$lang['form_validation_alpha']			= '{field} får endast innehålla bokstäver.';
$lang['form_validation_alpha_numeric']		= '{field} får endast innehålla bokstäver och siffror.';
$lang['form_validation_alpha_numeric_spaces']	= '{field} får endast innehålla bokstäver, siffror och mellanslag.';
$lang['form_validation_alpha_dash']		= '{field} får endast innehålla bokstäver, siffror, understreck och bindestreck.';
$lang['form_validation_numeric']		= '{field} får endast innehålla ett tal.';
$lang['form_validation_is_numeric']		= '{field} får endast innehålla ett tal.';
$lang['form_validation_integer']		= '{field} måste vara ett heltal.';
$lang['form_validation_regex_match']		= '{field} har inte rätt format.';
$lang['form_validation_matches']		= '{field} är inte samma som fältet {param}.';
$lang['form_validation_differs']		= '{field} måste skilja sig från fältet {param}.';
$lang['form_validation_is_unique'] 		= '{field} måste vara unikt.';
$lang['form_validation_is_natural']		= '{field} får endast innehålla ett positivt heltal eller noll.';
$lang['form_validation_is_natural_no_zero']	= '{field} får endast innehålla ett positivt heltal och inte noll.';
$lang['form_validation_decimal']		= '{field} måste innehålla ett decimaltal.';
$lang['form_validation_less_than']		= '{field} måste innehålla ett tal mindre än {param}.';
$lang['form_validation_less_than_equal_to']	= '{field} måste innehålla ett tal mindre än eller lika med {param}.';
$lang['form_validation_greater_than']		= '{field} måste innehålla ett tal större än {param}.';
$lang['form_validation_greater_than_equal_to']	= '{field} måste innehålla ett tal större än eller lika med {param}.';
$lang['form_validation_error_message_not_set']	= 'Kan inte hitta ett felmeddelande kopplat till fältet {field}.';
$lang['form_validation_in_list']		= '{field} måste vara en av: {param}.';
