<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudKMS;

class EkmConnection extends \Google\Collection
{
  protected $collection_key = 'serviceResolvers';
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $etag;
  /**
   * @var string
   */
  public $name;
  protected $serviceResolversType = ServiceResolver::class;
  protected $serviceResolversDataType = 'array';

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setEtag($etag)
  {
    $this->etag = $etag;
  }
  /**
   * @return string
   */
  public function getEtag()
  {
    return $this->etag;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param ServiceResolver[]
   */
  public function setServiceResolvers($serviceResolvers)
  {
    $this->serviceResolvers = $serviceResolvers;
  }
  /**
   * @return ServiceResolver[]
   */
  public function getServiceResolvers()
  {
    return $this->serviceResolvers;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(EkmConnection::class, 'Google_Service_CloudKMS_EkmConnection');
