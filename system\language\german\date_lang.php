<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = 'Jahr';
$lang['date_years'] = 'Jahre';
$lang['date_month'] = 'Monat';
$lang['date_months'] = 'Monate';
$lang['date_week'] = 'Woche';
$lang['date_weeks'] = 'Wochen';
$lang['date_day'] = 'Tag';
$lang['date_days'] = 'Tage';
$lang['date_hour'] = 'Stunde';
$lang['date_hours'] = 'Stunden';
$lang['date_minute'] = 'Minute';
$lang['date_minutes'] = 'Minuten';
$lang['date_second'] = 'Sekunde';
$lang['date_seconds'] = 'Sekunden';

$lang['UM12']	= '(UTC -12:00) Baker/Howland Insel';
$lang['UM11']	= '(UTC -11:00) Niue';
$lang['UM10']	= '(UTC -10:00) Hawaii-Aleuten-Standardzeit , Cook Inseln, Tahiti';
$lang['UM95']	= '(UTC -9:30) Marquesas Inseln';
$lang['UM9']	= '(UTC -9:00) Alaska Standardzeit, Gambier Inseln';
$lang['UM8']	= '(UTC -8:00) Pacific Normalzeit, Clipperton Insel';
$lang['UM7']	= '(UTC -7:00) Mountain Standardzeit';
$lang['UM6']	= '(UTC -6:00) Central StandardZeit';
$lang['UM5']	= '(UTC -5:00) Eastern Standardzeit, West-Karibische Standardzeit';
$lang['UM45']	= '(UTC -4:30) Venezuelische Standardzeit';
$lang['UM4']	= '(UTC -4:00) Atlantik Standardzeit, Ostst-Karibische Standardzeit';
$lang['UM35']	= '(UTC -3:30) Neufundland Standardzeit';
$lang['UM3']	= '(UTC -3:00) Argentinien, Brasilien, Französisch-Guayana, Uruguay';
$lang['UM2']	= '(UTC -2:00) Süd-Georgia / Südliche Sandwich Inseln';
$lang['UM1']	= '(UTC -1:00) Azoren, Cape Verde Inseln';
$lang['UTC']	= '(UTC) Greenwich Mean Zeit (GMT), Westeuropäische Zeit';
$lang['UP1']	= '(UTC +1:00) Mitteleuropäische Zeit (MEZ), Westafrikanische Zeit';
$lang['UP2']	= '(UTC +2:00) Zentralafrikanische Zeit, Osteuropäische Zeit, Kaliningrad Zeit';
$lang['UP3']	= '(UTC +3:00) Moskau Zeit, Ostafrikanische Zeit, Arabien Standardzeit';
$lang['UP35']	= '(UTC +3:30) Iran Standardzeit';
$lang['UP4']	= '(UTC +4:00) Azerbaijan Standardzeit, Samara Zeit';
$lang['UP45']	= '(UTC +4:30) Afghanistan';
$lang['UP5']	= '(UTC +5:00) Pakistan Standardzeit, Yekaterinburg Zeit';
$lang['UP55']	= '(UTC +5:30) Indische Standardzeit, Sri Lanka Zeit';
$lang['UP575']	= '(UTC +5:45) Nepal Zeit';
$lang['UP6']	= '(UTC +6:00) Bangladesh Standardzeit, Bhutan Zeit, Omsk Zeit';
$lang['UP65']	= '(UTC +6:30) Kokos Inseln, Myanmar';
$lang['UP7']	= '(UTC +7:00) Krasnoyarsk Zeit, Kambodscha, Laos, Thailand, Vietnam';
$lang['UP8']	= '(UTC +8:00) Australische Westliche Standardzeit, Beijing Zeit, Irkutsk Zeit';
$lang['UP875']	= '(UTC +8:45) Australische Zentral-Westliche Standardzeit';
$lang['UP9']	= '(UTC +9:00) Japan Standardzeit, Korea Standardzeit, Yakutsk Zeit';
$lang['UP95']	= '(UTC +9:30) Australische Zentral Standardzeit';
$lang['UP10']	= '(UTC +10:00) Australische Östliche Standardzeit, Vladivostok Zeit';
$lang['UP105']	= '(UTC +10:30) Lord Howe Insel';
$lang['UP11']	= '(UTC +11:00) Srednekolymsk Zeit, Solomon Inseln, Vanuatu';
$lang['UP115']	= '(UTC +11:30) Norfolk Insel';
$lang['UP12']	= '(UTC +12:00) Fidschi, Gilbert Inseln, Kamchatka Zeit, Neuseeland Standardzeit';
$lang['UP1275']	= '(UTC +12:45) Chatham Inseln Standardzeit';
$lang['UP13']	= '(UTC +13:00) Samoa Zeit Zone, Phoenix Inseln Zeit, Tonga';
$lang['UP14']	= '(UTC +14:00) Line Inseln';
