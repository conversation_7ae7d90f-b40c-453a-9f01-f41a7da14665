<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Apigee;

class GoogleCloudApigeeV1OperationGroup extends \Google\Collection
{
  protected $collection_key = 'operationConfigs';
  /**
   * @var string
   */
  public $operationConfigType;
  protected $operationConfigsType = GoogleCloudApigeeV1OperationConfig::class;
  protected $operationConfigsDataType = 'array';

  /**
   * @param string
   */
  public function setOperationConfigType($operationConfigType)
  {
    $this->operationConfigType = $operationConfigType;
  }
  /**
   * @return string
   */
  public function getOperationConfigType()
  {
    return $this->operationConfigType;
  }
  /**
   * @param GoogleCloudApigeeV1OperationConfig[]
   */
  public function setOperationConfigs($operationConfigs)
  {
    $this->operationConfigs = $operationConfigs;
  }
  /**
   * @return GoogleCloudApigeeV1OperationConfig[]
   */
  public function getOperationConfigs()
  {
    return $this->operationConfigs;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudApigeeV1OperationGroup::class, 'Google_Service_Apigee_GoogleCloudApigeeV1OperationGroup');
