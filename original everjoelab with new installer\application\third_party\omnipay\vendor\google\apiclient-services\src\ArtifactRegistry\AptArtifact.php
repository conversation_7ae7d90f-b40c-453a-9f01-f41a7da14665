<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\ArtifactRegistry;

class AptArtifact extends \Google\Model
{
  /**
   * @var string
   */
  public $architecture;
  /**
   * @var string
   */
  public $component;
  /**
   * @var string
   */
  public $controlFile;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $packageName;
  /**
   * @var string
   */
  public $packageType;

  /**
   * @param string
   */
  public function setArchitecture($architecture)
  {
    $this->architecture = $architecture;
  }
  /**
   * @return string
   */
  public function getArchitecture()
  {
    return $this->architecture;
  }
  /**
   * @param string
   */
  public function setComponent($component)
  {
    $this->component = $component;
  }
  /**
   * @return string
   */
  public function getComponent()
  {
    return $this->component;
  }
  /**
   * @param string
   */
  public function setControlFile($controlFile)
  {
    $this->controlFile = $controlFile;
  }
  /**
   * @return string
   */
  public function getControlFile()
  {
    return $this->controlFile;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setPackageName($packageName)
  {
    $this->packageName = $packageName;
  }
  /**
   * @return string
   */
  public function getPackageName()
  {
    return $this->packageName;
  }
  /**
   * @param string
   */
  public function setPackageType($packageType)
  {
    $this->packageType = $packageType;
  }
  /**
   * @return string
   */
  public function getPackageType()
  {
    return $this->packageType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AptArtifact::class, 'Google_Service_ArtifactRegistry_AptArtifact');
