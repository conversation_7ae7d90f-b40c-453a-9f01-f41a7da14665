<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Video_conference_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->current_session = $this->setting_model->getCurrentSession();
    }

    public function get($id = null)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname, staff.employee_id as host_employee_id');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        
        if ($id != null) {
            $this->db->where('video_conferences.id', $id);
        } else {
            $this->db->order_by('video_conferences.start_time', 'DESC');
        }
        
        $query = $this->db->get();
        if ($id != null) {
            return $query->row();
        } else {
            return $query->result();
        }
    }

    public function add($data)
    {
        $this->db->trans_start();
        $this->db->insert('video_conferences', $data);
        $insert_id = $this->db->insert_id();
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === FALSE) {
            return false;
        }
        return $insert_id;
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update('video_conferences', $data);
        return $this->db->affected_rows();
    }

    public function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete('video_conferences');
        return $this->db->affected_rows();
    }

    public function getByClass($class_id, $section_id = null)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        $this->db->where('video_conferences.class_id', $class_id);
        
        if ($section_id != null) {
            $this->db->where('video_conferences.section_id', $section_id);
        }
        
        $this->db->order_by('video_conferences.start_time', 'DESC');
        $query = $this->db->get();
        return $query->result();
    }

    public function getUpcoming($limit = 10)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        $this->db->where('video_conferences.start_time >', date('Y-m-d H:i:s'));
        $this->db->where('video_conferences.status', 'scheduled');
        $this->db->order_by('video_conferences.start_time', 'ASC');
        $this->db->limit($limit);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function getOngoing()
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        $this->db->where('video_conferences.start_time <=', date('Y-m-d H:i:s'));
        $this->db->where('video_conferences.end_time >=', date('Y-m-d H:i:s'));
        $this->db->where('video_conferences.status', 'ongoing');
        $this->db->order_by('video_conferences.start_time', 'ASC');
        
        $query = $this->db->get();
        return $query->result();
    }

    public function getByPlatform($platform)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        $this->db->where('video_conferences.platform', $platform);
        $this->db->order_by('video_conferences.start_time', 'DESC');
        
        $query = $this->db->get();
        return $query->result();
    }

    public function updateStatus($id, $status)
    {
        $data = array(
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id);
        $this->db->update('video_conferences', $data);
        return $this->db->affected_rows();
    }

    public function addParticipant($conference_id, $participant_data)
    {
        $participant_data['conference_id'] = $conference_id;
        $this->db->insert('video_conference_participants', $participant_data);
        return $this->db->insert_id();
    }

    public function getParticipants($conference_id)
    {
        $this->db->select('video_conference_participants.*, students.firstname, students.lastname, students.admission_no,
                          staff.name as staff_name, staff.surname as staff_surname, staff.employee_id');
        $this->db->from('video_conference_participants');
        $this->db->join('students', 'students.id = video_conference_participants.participant_id AND video_conference_participants.participant_type = "student"', 'left');
        $this->db->join('staff', 'staff.id = video_conference_participants.participant_id AND video_conference_participants.participant_type = "staff"', 'left');
        $this->db->where('video_conference_participants.conference_id', $conference_id);
        $this->db->order_by('video_conference_participants.join_time', 'ASC');
        
        $query = $this->db->get();
        return $query->result();
    }

    public function updateParticipantStatus($conference_id, $participant_id, $participant_type, $status_data)
    {
        $this->db->where('conference_id', $conference_id);
        $this->db->where('participant_id', $participant_id);
        $this->db->where('participant_type', $participant_type);
        $this->db->update('video_conference_participants', $status_data);
        return $this->db->affected_rows();
    }

    public function getStudentMeetings($student_id, $limit = 10)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname, 
                          video_conference_participants.status as participation_status,
                          video_conference_participants.join_time, video_conference_participants.leave_time');
        $this->db->from('video_conferences');
        $this->db->join('video_conference_participants', 'video_conference_participants.conference_id = video_conferences.id');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        $this->db->where('video_conference_participants.participant_id', $student_id);
        $this->db->where('video_conference_participants.participant_type', 'student');
        $this->db->order_by('video_conferences.start_time', 'DESC');
        $this->db->limit($limit);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function getStaffMeetings($staff_id, $limit = 10)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->where('video_conferences.host_id', $staff_id);
        $this->db->order_by('video_conferences.start_time', 'DESC');
        $this->db->limit($limit);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function getReports($start_date = null, $end_date = null, $platform = null)
    {
        $this->db->select('video_conferences.*, classes.class, sections.section, subjects.name as subject_name, 
                          staff.name as host_name, staff.surname as host_surname,
                          COUNT(video_conference_participants.id) as total_participants,
                          SUM(CASE WHEN video_conference_participants.status = "joined" THEN 1 ELSE 0 END) as joined_participants');
        $this->db->from('video_conferences');
        $this->db->join('classes', 'classes.id = video_conferences.class_id', 'left');
        $this->db->join('sections', 'sections.id = video_conferences.section_id', 'left');
        $this->db->join('subjects', 'subjects.id = video_conferences.subject_id', 'left');
        $this->db->join('staff', 'staff.id = video_conferences.host_id', 'left');
        $this->db->join('video_conference_participants', 'video_conference_participants.conference_id = video_conferences.id', 'left');
        
        if ($start_date) {
            $this->db->where('video_conferences.start_time >=', $start_date);
        }
        if ($end_date) {
            $this->db->where('video_conferences.start_time <=', $end_date);
        }
        if ($platform) {
            $this->db->where('video_conferences.platform', $platform);
        }
        
        $this->db->group_by('video_conferences.id');
        $this->db->order_by('video_conferences.start_time', 'DESC');
        
        $query = $this->db->get();
        return $query->result();
    }
}
