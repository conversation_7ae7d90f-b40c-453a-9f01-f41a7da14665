<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'Il campo {field} è necessario.';
$lang['form_validation_isset'] = 'Il campo {field} deve avere un valore.';
$lang['form_validation_valid_email'] = 'Il campo {field} deve contenere un indirizzo email valido.';
$lang['form_validation_valid_emails'] = 'Il campo {field} deve contenere tutti indirizzi email validi.';
$lang['form_validation_valid_url'] = 'Il campo {field} deve contenere un URL valido.';
$lang['form_validation_valid_ip'] = 'Il campo {field} deve contenenre un indirizzo IP valido.';
$lang['form_validation_min_length'] = 'Il campo {field} deve essere composto da almeno {param} caratteri.';
$lang['form_validation_max_length'] = 'Il campo {field} deve essere composto da meno di {param} caratteri.';
$lang['form_validation_exact_length'] = 'Il campo {field} deve essere composto esattamente da {param} caratetteri.';
$lang['form_validation_alpha'] = 'Il campo {field} deve contenere solo caratterti alfabetici.';
$lang['form_validation_alpha_numeric'] = 'Il campo {field} deve contenere solo caratteri alfa-numerici.';
$lang['form_validation_alpha_numeric_spaces'] = 'Il campo {field} deve contenere solo caratteri alfa-numerici e spazi.';
$lang['form_validation_alpha_dash'] = 'Il campo {field} deve contenere solo caratteri alfa-numerici, underscore e trattini.';
$lang['form_validation_numeric'] = 'Il campo {field} deve contenere un numero.';
$lang['form_validation_is_numeric'] = 'Il campo {field} deve contenere un numero.';
$lang['form_validation_integer'] = 'Il campo {field} deve contenere un intero.';
$lang['form_validation_regex_match'] = 'Il campo {field} non è stato inserito nella forma corretta.';
$lang['form_validation_matches'] = 'Il campo {field} non è uguale al campo {param}.';
$lang['form_validation_differs'] = 'Il campo {field} deve essere differente dal campo {param}.';
$lang['form_validation_is_unique'] = 'Il campo {field} deve contenere un valore univoco.';
$lang['form_validation_is_natural'] = 'Il campo {field} deve contenenre un numero.';
$lang['form_validation_is_natural_no_zero'] = 'Il campo {field} deve contenere un numero più grande di zero.';
$lang['form_validation_decimal'] = 'Il campo {field} deve contenere un numero decimale.';
$lang['form_validation_less_than'] = 'Il campo {field} deve contenere un numero inferiore a {param}.';
$lang['form_validation_less_than_equal_to'] = 'Il campo {field} deve contenere un numero inferiore o uguale a {param}.';
$lang['form_validation_greater_than'] = 'Il campo {field} deve contenere un numero maggiore di {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Il campo {field} deve contenere un numero maggiore o uguale a {param}.';
$lang['form_validation_error_message_not_set']  = 'Non è stato possibile trovare il messaggio di errore relativo al campo {field}';
$lang['form_validation_in_list']		= 'Il campo {field} può contenere solo uno dei seguenti valori: {param}.';
