<?php
// Enable all payment gateways script

// Set up environment
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/';

// Include CodeIgniter bootstrap
require_once 'index.php';

// Get CI instance
$CI =& get_instance();

// Load the payment setting model
$CI->load->model('paymentsetting_model');

echo "Initializing default payment gateways...\n";
$result = $CI->paymentsetting_model->initializeDefaultGateways();

if ($result) {
    echo "Default gateways initialized successfully.\n";
    
    // Enable all gateways
    echo "Enabling all payment gateways...\n";
    $affected_rows = $CI->paymentsetting_model->enableAllGateways();
    echo "Enabled " . $affected_rows . " payment gateways.\n";
    
    // Get current status
    $gateways = $CI->paymentsetting_model->get();
    echo "Current gateway status:\n";
    foreach ($gateways as $gateway) {
        echo "- " . $gateway->payment_type . ": " . $gateway->is_active . "\n";
    }
    
    echo "\nAll payment gateways have been enabled successfully!\n";
    echo "Customers can now choose from " . count($gateways) . " different payment methods.\n";
} else {
    echo "Failed to initialize gateways.\n";
}
?>
