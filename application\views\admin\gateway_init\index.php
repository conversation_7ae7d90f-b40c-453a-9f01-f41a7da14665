<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-credit-card"></i> Payment Gateway Initialization
            <small>Initialize and manage all payment gateways</small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="<?php echo base_url(); ?>admin/dashboard"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="<?php echo base_url(); ?>admin/paymentsettings">Payment Settings</a></li>
            <li class="active">Gateway Initialization</li>
        </ol>
    </section>

    <section class="content">
        <?php if ($this->session->flashdata('msg')) { ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h4><i class="icon fa fa-check"></i> Success!</h4>
                <?php echo $this->session->flashdata('msg'); ?>
            </div>
        <?php } ?>

        <div class="row">
            <div class="col-md-8">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-list"></i> Available Payment Gateways</h3>
                    </div>
                    <div class="box-body">
                        <div class="alert alert-info">
                            <h4><i class="fa fa-info-circle"></i> Gateway Status</h4>
                            <p>Below are all 24 supported payment gateways. You can initialize missing gateways and enable them for customer use.</p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>Gateway Name</th>
                                        <th>Status</th>
                                        <th>Database Status</th>
                                        <th>Active</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $current_gateway_types = array();
                                    foreach ($current_gateways as $cg) {
                                        $current_gateway_types[$cg->payment_type] = $cg;
                                    }
                                    
                                    foreach ($gateways as $gateway): 
                                        $exists = isset($current_gateway_types[$gateway]);
                                        $is_active = $exists && $current_gateway_types[$gateway]->is_active == 'yes';
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo ucfirst($gateway); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <span class="label label-success">
                                                    <i class="fa fa-check"></i> Initialized
                                                </span>
                                            <?php else: ?>
                                                <span class="label label-warning">
                                                    <i class="fa fa-exclamation-triangle"></i> Not Initialized
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <span class="text-success">
                                                    <i class="fa fa-database"></i> In Database
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fa fa-minus-circle"></i> Missing
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($is_active): ?>
                                                <span class="label label-success">
                                                    <i class="fa fa-check-circle"></i> Active
                                                </span>
                                            <?php else: ?>
                                                <span class="label label-default">
                                                    <i class="fa fa-circle-o"></i> Inactive
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-cogs"></i> Quick Actions</h3>
                    </div>
                    <div class="box-body">
                        <div class="btn-group-vertical btn-block">
                            <a href="<?php echo base_url('admin/gateway_init/initialize_all'); ?>" 
                               class="btn btn-primary btn-lg"
                               onclick="return confirm('This will initialize all missing payment gateways in the database. Continue?')">
                                <i class="fa fa-database"></i> Initialize All Gateways
                            </a>
                            
                            <a href="<?php echo base_url('admin/gateway_init/enable_all'); ?>" 
                               class="btn btn-success btn-lg"
                               onclick="return confirm('This will enable ALL 24 payment gateways for customer use. Customers will be able to choose from all payment methods. Continue?')">
                                <i class="fa fa-check-circle"></i> Enable All Gateways
                            </a>
                            
                            <a href="<?php echo base_url('admin/paymentsettings'); ?>" 
                               class="btn btn-info btn-lg">
                                <i class="fa fa-arrow-left"></i> Back to Payment Settings
                            </a>
                        </div>

                        <div class="alert alert-warning" style="margin-top: 20px;">
                            <h5><i class="fa fa-warning"></i> Important Notes</h5>
                            <ul style="margin-bottom: 0;">
                                <li>Initialize gateways first before enabling them</li>
                                <li>Configure API credentials for each gateway before going live</li>
                                <li>Test payments in demo mode first</li>
                                <li>Multiple gateways give customers more payment options</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-bar-chart"></i> Statistics</h3>
                    </div>
                    <div class="box-body">
                        <?php 
                        $total_gateways = count($gateways);
                        $initialized_count = count($current_gateways);
                        $active_count = 0;
                        foreach ($current_gateways as $cg) {
                            if ($cg->is_active == 'yes') $active_count++;
                        }
                        ?>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="info-box bg-blue">
                                    <span class="info-box-icon"><i class="fa fa-credit-card"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Gateways</span>
                                        <span class="info-box-number"><?php echo $total_gateways; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="info-box bg-green">
                                    <span class="info-box-icon"><i class="fa fa-database"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Initialized</span>
                                        <span class="info-box-number"><?php echo $initialized_count; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="info-box bg-yellow">
                                    <span class="info-box-icon"><i class="fa fa-check-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Active</span>
                                        <span class="info-box-number"><?php echo $active_count; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
