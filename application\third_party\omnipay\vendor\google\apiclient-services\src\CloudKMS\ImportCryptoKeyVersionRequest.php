<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudKMS;

class ImportCryptoKeyVersionRequest extends \Google\Model
{
  /**
   * @var string
   */
  public $algorithm;
  /**
   * @var string
   */
  public $cryptoKeyVersion;
  /**
   * @var string
   */
  public $importJob;
  /**
   * @var string
   */
  public $rsaAesWrappedKey;
  /**
   * @var string
   */
  public $wrappedKey;

  /**
   * @param string
   */
  public function setAlgorithm($algorithm)
  {
    $this->algorithm = $algorithm;
  }
  /**
   * @return string
   */
  public function getAlgorithm()
  {
    return $this->algorithm;
  }
  /**
   * @param string
   */
  public function setCryptoKeyVersion($cryptoKeyVersion)
  {
    $this->cryptoKeyVersion = $cryptoKeyVersion;
  }
  /**
   * @return string
   */
  public function getCryptoKeyVersion()
  {
    return $this->cryptoKeyVersion;
  }
  /**
   * @param string
   */
  public function setImportJob($importJob)
  {
    $this->importJob = $importJob;
  }
  /**
   * @return string
   */
  public function getImportJob()
  {
    return $this->importJob;
  }
  /**
   * @param string
   */
  public function setRsaAesWrappedKey($rsaAesWrappedKey)
  {
    $this->rsaAesWrappedKey = $rsaAesWrappedKey;
  }
  /**
   * @return string
   */
  public function getRsaAesWrappedKey()
  {
    return $this->rsaAesWrappedKey;
  }
  /**
   * @param string
   */
  public function setWrappedKey($wrappedKey)
  {
    $this->wrappedKey = $wrappedKey;
  }
  /**
   * @return string
   */
  public function getWrappedKey()
  {
    return $this->wrappedKey;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ImportCryptoKeyVersionRequest::class, 'Google_Service_CloudKMS_ImportCryptoKeyVersionRequest');
