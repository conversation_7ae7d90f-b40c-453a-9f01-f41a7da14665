<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-credit-card"></i> <?php echo $this->lang->line('payment_gateway_status'); ?>
            <small><?php echo $this->lang->line('manage_all_payment_gateways'); ?></small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> <?php echo $this->lang->line('dashboard'); ?></a></li>
            <li><a href="<?php echo base_url('admin/paymentsettings'); ?>"><i class="fa fa-credit-card"></i> <?php echo $this->lang->line('payment_methods'); ?></a></li>
            <li class="active"><?php echo $this->lang->line('gateway_status'); ?></li>
        </ol>
    </section>

    <section class="content">
        <div class="row">
            <?php if ($this->session->flashdata('msg')) { echo $this->session->flashdata('msg'); } ?>
            
            <!-- Quick Stats -->
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-green">
                    <div class="inner">
                        <h3><?php echo $active_count; ?></h3>
                        <p><?php echo $this->lang->line('active_gateways'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-check-circle"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-blue">
                    <div class="inner">
                        <h3><?php echo count($all_gateways); ?></h3>
                        <p><?php echo $this->lang->line('total_gateways'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-credit-card"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-yellow">
                    <div class="inner">
                        <h3><?php echo $configured_count; ?></h3>
                        <p><?php echo $this->lang->line('configured_gateways'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-cog"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-red">
                    <div class="inner">
                        <h3><?php echo count($all_gateways) - $active_count; ?></h3>
                        <p><?php echo $this->lang->line('inactive_gateways'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-times-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-bolt"></i> <?php echo $this->lang->line('quick_actions'); ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <?php if ($this->rbac->hasPrivilege('payment_methods', 'can_edit')) { ?>
                                <a href="<?php echo base_url('admin/paymentsettings/enable_all_gateways'); ?>" class="btn btn-success btn-block" onclick="return confirm('This will enable all payment gateways. Are you sure?')">
                                    <i class="fa fa-check-circle"></i> <?php echo $this->lang->line('enable_all_gateways'); ?>
                                </a>
                                <?php } ?>
                            </div>
                            <div class="col-md-3">
                                <a href="<?php echo base_url('admin/paymentsettings'); ?>" class="btn btn-primary btn-block">
                                    <i class="fa fa-cog"></i> <?php echo $this->lang->line('configure_gateways'); ?>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-info btn-block" onclick="location.reload()">
                                    <i class="fa fa-refresh"></i> <?php echo $this->lang->line('refresh_status'); ?>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <a href="<?php echo base_url('admin/paymentsettings'); ?>" class="btn btn-default btn-block">
                                    <i class="fa fa-arrow-left"></i> <?php echo $this->lang->line('back_to_settings'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gateway Status Table -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-list"></i> <?php echo $this->lang->line('all_payment_gateways'); ?></h3>
                        <div class="box-tools pull-right">
                            <span class="label label-info"><?php echo count($all_gateways); ?> <?php echo $this->lang->line('total'); ?></span>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover" id="gateway-table">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('gateway_name'); ?></th>
                                        <th><?php echo $this->lang->line('status'); ?></th>
                                        <th><?php echo $this->lang->line('configuration'); ?></th>
                                        <th><?php echo $this->lang->line('last_updated'); ?></th>
                                        <th class="text-right"><?php echo $this->lang->line('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    // Create a lookup array for existing gateways
                                    $existing_gateways = array();
                                    foreach ($paymentlist as $gateway) {
                                        $existing_gateways[$gateway->payment_type] = $gateway;
                                    }
                                    
                                    foreach ($all_gateways as $gateway_name) { 
                                        $gateway = isset($existing_gateways[$gateway_name]) ? $existing_gateways[$gateway_name] : null;
                                        $is_active = $gateway && $gateway->is_active == 'yes';
                                        $is_configured = $gateway && (!empty($gateway->api_publishable_key) || !empty($gateway->api_secret_key));
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo ucfirst(str_replace('_', ' ', $gateway_name)); ?></strong>
                                            <br><small class="text-muted"><?php echo $gateway_name; ?></small>
                                        </td>
                                        <td>
                                            <?php if ($is_active) { ?>
                                                <span class="label label-success">
                                                    <i class="fa fa-check"></i> <?php echo $this->lang->line('active'); ?>
                                                </span>
                                            <?php } else { ?>
                                                <span class="label label-danger">
                                                    <i class="fa fa-times"></i> <?php echo $this->lang->line('inactive'); ?>
                                                </span>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <?php if ($is_configured) { ?>
                                                <span class="label label-info">
                                                    <i class="fa fa-cog"></i> <?php echo $this->lang->line('configured'); ?>
                                                </span>
                                            <?php } else { ?>
                                                <span class="label label-warning">
                                                    <i class="fa fa-exclamation-triangle"></i> <?php echo $this->lang->line('not_configured'); ?>
                                                </span>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <?php if ($gateway && $gateway->created_at) { ?>
                                                <?php echo date('M d, Y H:i', strtotime($gateway->created_at)); ?>
                                            <?php } else { ?>
                                                <span class="text-muted">-</span>
                                            <?php } ?>
                                        </td>
                                        <td class="text-right">
                                            <div class="btn-group">
                                                <?php if ($this->rbac->hasPrivilege('payment_methods', 'can_edit')) { ?>
                                                    <?php if ($is_active) { ?>
                                                    <button type="button" class="btn btn-danger btn-xs toggle-gateway" 
                                                            data-gateway="<?php echo $gateway_name; ?>" data-action="disable">
                                                        <i class="fa fa-times"></i> <?php echo $this->lang->line('disable'); ?>
                                                    </button>
                                                    <?php } else { ?>
                                                    <button type="button" class="btn btn-success btn-xs toggle-gateway" 
                                                            data-gateway="<?php echo $gateway_name; ?>" data-action="enable">
                                                        <i class="fa fa-check"></i> <?php echo $this->lang->line('enable'); ?>
                                                    </button>
                                                    <?php } ?>
                                                <?php } ?>
                                                <a href="<?php echo base_url('admin/paymentsettings#tab_' . (array_search($gateway_name, $all_gateways) + 1)); ?>" 
                                                   class="btn btn-info btn-xs">
                                                    <i class="fa fa-cog"></i> <?php echo $this->lang->line('configure'); ?>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gateway Information -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-info-circle"></i> <?php echo $this->lang->line('gateway_information'); ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="alert alert-info">
                            <h4><i class="fa fa-info-circle"></i> <?php echo $this->lang->line('important_notes'); ?></h4>
                            <ul>
                                <li><?php echo $this->lang->line('gateway_note_1'); ?></li>
                                <li><?php echo $this->lang->line('gateway_note_2'); ?></li>
                                <li><?php echo $this->lang->line('gateway_note_3'); ?></li>
                                <li><?php echo $this->lang->line('gateway_note_4'); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    $('#gateway-table').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "order": [[ 0, "asc" ]], // Sort by gateway name
        "columnDefs": [
            { "orderable": false, "targets": 4 } // Disable sorting on action column
        ]
    });

    // Handle gateway toggle
    $('.toggle-gateway').click(function() {
        var gateway = $(this).data('gateway');
        var action = $(this).data('action');
        var button = $(this);
        
        $.ajax({
            url: '<?php echo base_url("admin/paymentsettings/toggle_gateway"); ?>',
            type: 'POST',
            data: {
                payment_type: gateway,
                status: action
            },
            dataType: 'json',
            beforeSend: function() {
                button.prop('disabled', true);
                button.html('<i class="fa fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                if (response.st == 0) {
                    location.reload();
                } else {
                    alert('Error: ' + response.msg);
                    button.prop('disabled', false);
                    button.html('<i class="fa fa-' + (action == 'enable' ? 'check' : 'times') + '"></i> ' + action.charAt(0).toUpperCase() + action.slice(1));
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
                button.prop('disabled', false);
                button.html('<i class="fa fa-' + (action == 'enable' ? 'check' : 'times') + '"></i> ' + action.charAt(0).toUpperCase() + action.slice(1));
            }
        });
    });
});
</script>
