<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Jitsi_lib
{
    protected $CI;
    protected $domain = 'meet.jit.si';
    protected $custom_domain;
    protected $jwt_secret;
    protected $app_id;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('video_conference_settings_model');
        $this->loadSettings();
    }

    private function loadSettings()
    {
        $settings = $this->CI->video_conference_settings_model->getJitsiSettings();
        if ($settings) {
            $settings = $this->CI->video_conference_settings_model->decryptSensitiveData($settings);
            $this->custom_domain = $settings->api_key ?? null; // Using api_key for custom domain
            $this->jwt_secret = $settings->api_secret ?? null;
            $this->app_id = $settings->client_id ?? null;
            
            if (!empty($this->custom_domain)) {
                $this->domain = $this->custom_domain;
            }
        }
    }

    public function createMeeting($meetingData)
    {
        // Generate a unique room name
        $roomName = $this->generateRoomName($meetingData['title']);
        
        // Create meeting URL
        $meetingUrl = $this->generateMeetingUrl($roomName);
        
        // Generate JWT token if configured for secure domain
        $jwt_token = null;
        if (!empty($this->jwt_secret) && !empty($this->app_id)) {
            $jwt_token = $this->generateJWT($roomName, $meetingData);
        }

        return [
            'success' => true,
            'meeting_id' => $roomName,
            'meeting_url' => $meetingUrl,
            'jwt_token' => $jwt_token,
            'embed_url' => $this->generateEmbedUrl($roomName, $jwt_token),
            'response' => [
                'room_name' => $roomName,
                'domain' => $this->domain,
                'start_time' => $meetingData['start_time'],
                'duration' => $meetingData['duration']
            ]
        ];
    }

    public function updateMeeting($meetingId, $meetingData)
    {
        // Jitsi doesn't have a traditional update API
        // We can regenerate the meeting with new parameters
        return $this->createMeeting($meetingData);
    }

    public function deleteMeeting($meetingId)
    {
        // Jitsi rooms are automatically cleaned up
        // No explicit deletion needed
        return [
            'success' => true,
            'message' => 'Meeting room will be automatically cleaned up'
        ];
    }

    public function getMeeting($meetingId)
    {
        // Generate meeting info based on stored data
        $meetingUrl = $this->generateMeetingUrl($meetingId);
        
        return [
            'success' => true,
            'meeting' => [
                'id' => $meetingId,
                'url' => $meetingUrl,
                'domain' => $this->domain
            ]
        ];
    }

    private function generateRoomName($title)
    {
        // Create a URL-friendly room name
        $roomName = strtolower(trim($title));
        $roomName = preg_replace('/[^a-z0-9\-]/', '-', $roomName);
        $roomName = preg_replace('/-+/', '-', $roomName);
        $roomName = trim($roomName, '-');
        
        // Add timestamp to ensure uniqueness
        $roomName .= '-' . time();
        
        return $roomName;
    }

    private function generateMeetingUrl($roomName, $jwt_token = null)
    {
        $url = "https://{$this->domain}/{$roomName}";
        
        if ($jwt_token) {
            $url .= "?jwt={$jwt_token}";
        }
        
        return $url;
    }

    private function generateEmbedUrl($roomName, $jwt_token = null)
    {
        $config = array(
            'roomName' => $roomName,
            'width' => '100%',
            'height' => '600',
            'parentNode' => 'jitsi-container',
            'configOverwrite' => array(
                'startWithAudioMuted' => true,
                'startWithVideoMuted' => false,
                'enableWelcomePage' => false,
                'prejoinPageEnabled' => false
            ),
            'interfaceConfigOverwrite' => array(
                'TOOLBAR_BUTTONS' => array(
                    'microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen',
                    'fodeviceselection', 'hangup', 'profile', 'chat', 'recording',
                    'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',
                    'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
                    'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone'
                )
            )
        );

        if ($jwt_token) {
            $config['jwt'] = $jwt_token;
        }

        return json_encode($config);
    }

    private function generateJWT($roomName, $meetingData)
    {
        if (empty($this->jwt_secret) || empty($this->app_id)) {
            return null;
        }

        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        
        $payload = json_encode([
            'iss' => $this->app_id,
            'aud' => $this->app_id,
            'exp' => strtotime($meetingData['start_time']) + ($meetingData['duration'] * 60) + 3600, // Meeting duration + 1 hour buffer
            'nbf' => strtotime($meetingData['start_time']) - 300, // 5 minutes before meeting
            'sub' => $this->domain,
            'room' => $roomName,
            'context' => [
                'user' => [
                    'moderator' => true,
                    'name' => $meetingData['host_name'] ?? 'Host',
                    'email' => $meetingData['host_email'] ?? '',
                    'avatar' => $meetingData['host_avatar'] ?? ''
                ],
                'features' => [
                    'livestreaming' => true,
                    'recording' => true,
                    'transcription' => true
                ]
            ]
        ]);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->jwt_secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    public function generateIframeCode($roomName, $width = '100%', $height = '600px', $jwt_token = null)
    {
        $url = $this->generateMeetingUrl($roomName, $jwt_token);
        
        return "<iframe 
                    src='{$url}' 
                    width='{$width}' 
                    height='{$height}' 
                    frameborder='0' 
                    allow='camera; microphone; fullscreen; display-capture'>
                </iframe>";
    }

    public function generateJavaScriptAPI($roomName, $containerId = 'jitsi-container', $jwt_token = null)
    {
        $config = $this->generateEmbedUrl($roomName, $jwt_token);
        
        return "
        <script src='https://{$this->domain}/external_api.js'></script>
        <script>
            const domain = '{$this->domain}';
            const options = {$config};
            const api = new JitsiMeetExternalAPI(domain, options);
            
            // Event listeners
            api.addEventListener('videoConferenceJoined', (data) => {
                console.log('User joined:', data);
            });
            
            api.addEventListener('videoConferenceLeft', (data) => {
                console.log('User left:', data);
            });
            
            api.addEventListener('participantJoined', (data) => {
                console.log('Participant joined:', data);
            });
            
            api.addEventListener('participantLeft', (data) => {
                console.log('Participant left:', data);
            });
        </script>";
    }

    public function testConnection()
    {
        // Test if Jitsi domain is accessible
        $url = "https://{$this->domain}/";
        
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_NOBODY => true
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpCode == 200) {
            return [
                'success' => true,
                'message' => 'Jitsi domain is accessible',
                'domain' => $this->domain
            ];
        }

        return [
            'success' => false,
            'message' => 'Jitsi domain is not accessible',
            'domain' => $this->domain,
            'http_code' => $httpCode
        ];
    }

    public function isConfigured()
    {
        // Jitsi works without configuration (using public meet.jit.si)
        // But returns true if custom domain or JWT is configured
        return true;
    }

    public function hasCustomDomain()
    {
        return !empty($this->custom_domain);
    }

    public function hasJWTEnabled()
    {
        return !empty($this->jwt_secret) && !empty($this->app_id);
    }

    public function getDomain()
    {
        return $this->domain;
    }

    public function setCustomDomain($domain)
    {
        $this->domain = $domain;
        $this->custom_domain = $domain;
    }
}
