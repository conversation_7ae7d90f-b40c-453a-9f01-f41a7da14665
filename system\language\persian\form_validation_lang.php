<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'مقداردهی فیلد {field} الزامی است.';
$lang['form_validation_isset'] = 'فیلد {field} بایستی دارای مقدار باشد.';
$lang['form_validation_valid_email'] = 'فیلد {field} بایستی با یک پست الکترونیک معتبر مقداردهی شود.';
$lang['form_validation_valid_emails'] = 'فیلد {field} بایستی تماماً با پست های الکترونیک معتبر مقداردهی شود.';
$lang['form_validation_valid_url'] = 'فیلد {field} باید شامل یک آدرس اینترنتی معتبر باشد.';
$lang['form_validation_valid_ip'] = 'فیلد {field} باید شامل یک آدرس اینترنتی(IP) معتبر باشد.';
$lang['form_validation_min_length'] = 'حداقل تعداد کاراکترهای مورد نیاز برای فیلد {field}، {param} کاراکتر است.';
$lang['form_validation_max_length'] = 'حداکثر تعداد کاراکترهای مجاز برای فیلد {field}، {param} کاراکتر است.';
$lang['form_validation_exact_length'] = 'طول مجاز برای فیلد {field}، {param} کاراکتر است.';
$lang['form_validation_alpha'] = 'فیلد {field} تنها باید شامل حروف باشد.';
$lang['form_validation_alpha_numeric'] = 'فیلد {field} تنها می تواند شامل حروف و ارقام باشد.';
$lang['form_validation_alpha_numeric_spaces'] = 'فیلد {field} تنها باید شامل حروف، ارقام و فاصله باشد.';
$lang['form_validation_alpha_dash'] = 'فیلد {field} تنها باید شامل حروف، ارقام، خط فاصله و زیرخط باشد.';
$lang['form_validation_numeric'] = 'فیلد {field} تنها باید شامل ارقام باشد.';
$lang['form_validation_is_numeric'] = 'فیلد {field} تنها باید شامل مقدار عددی باشد.';
$lang['form_validation_integer'] = 'فیلد {field} تنها باید شامل عدد صحیح باشد.';
$lang['form_validation_regex_match'] = 'فیلد {field} در قالب درست وارد نشده است.';
$lang['form_validation_matches'] = 'فیلد {field} با فیلد {param} یکی نیست.';
$lang['form_validation_differs'] = 'فیلد {field} باید با فیلد {param} متفاوت باشد.';
$lang['form_validation_is_unique'] = 'فیلد {field} باید شامل یک مقدار یکتا باشد.';
$lang['form_validation_is_natural'] = 'فیلد {field} تنها باید شامل اعداد طبیعی باشد.';
$lang['form_validation_is_natural_no_zero'] = 'فیلد {field} باید تنها شامل اعداد طبیعی بزرگتر از صفر باشد.';
$lang['form_validation_decimal'] = 'فیلد {field} باید تنها شامل اعداد اعشاری باشد.';
$lang['form_validation_less_than'] = 'فیلد {field} باید کمتر از {param} باشد';
$lang['form_validation_less_than_equal_to'] = 'فیلد {field} باید شامل یک مقدار کمتر یا مساوی {param} باشد.';
$lang['form_validation_greater_than'] = 'فیلد {field} باید شامل یک مقدار بزرگ تر از {param} باشد.';
$lang['form_validation_greater_than_equal_to'] = 'فیلد {field} باید شامل یک مقدار بیشتر یا مساوی {param} باشد.';
$lang['form_validation_error_message_not_set'] = 'دسترسی به خطای متناظر با فیلد {field} امکانپذیر نیست.';
$lang['form_validation_in_list'] = 'فیلد {field} باید دارای یکی از مقادیر روبرو باشد : {param}.';
