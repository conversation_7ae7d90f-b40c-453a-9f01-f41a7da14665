<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Appengine;

class EndpointsApiService extends \Google\Model
{
  /**
   * @var string
   */
  public $configId;
  /**
   * @var bool
   */
  public $disableTraceSampling;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $rolloutStrategy;

  /**
   * @param string
   */
  public function setConfigId($configId)
  {
    $this->configId = $configId;
  }
  /**
   * @return string
   */
  public function getConfigId()
  {
    return $this->configId;
  }
  /**
   * @param bool
   */
  public function setDisableTraceSampling($disableTraceSampling)
  {
    $this->disableTraceSampling = $disableTraceSampling;
  }
  /**
   * @return bool
   */
  public function getDisableTraceSampling()
  {
    return $this->disableTraceSampling;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setRolloutStrategy($rolloutStrategy)
  {
    $this->rolloutStrategy = $rolloutStrategy;
  }
  /**
   * @return string
   */
  public function getRolloutStrategy()
  {
    return $this->rolloutStrategy;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(EndpointsApiService::class, 'Google_Service_Appengine_EndpointsApiService');
