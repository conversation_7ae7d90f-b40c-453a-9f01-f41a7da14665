<?php
 /**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @copyright	Novak <PERSON>
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('Nije dozvoljen direktan pristup');

$lang['form_validation_required']		= 'Polje {field} je obavezno.';
$lang['form_validation_isset']			= 'Polje {field} mora imati vrednost.';
$lang['form_validation_valid_email']	= 'Polje {field} mora sadržati ispravnu email adresu.';
$lang['form_validation_valid_emails']	= 'Polje {field} mora sadržati sve ispravne email adrese.';
$lang['form_validation_valid_url']		= 'Polje {field} mora sadržati ispravan URL.';
$lang['form_validation_valid_ip']		= 'Polje {field} mora sadržati ispravnu IP adresu.';
$lang['form_validation_min_length']		= 'Polje {field} mora biti najmanje {param} karaktera dugačko.';
$lang['form_validation_max_length']		= 'Polje {field} ne može imati veću dužinu od {param} karaktera.';
$lang['form_validation_exact_length']	= 'Polje {field} mora imati dužinu tačno {param} karaktera.';
$lang['form_validation_alpha']			= 'Polje {field} mora sadržati samo slovne karaktere.';
$lang['form_validation_alpha_numeric']	= 'Polje {field} mora sadržati samo alfa-numeričke karaktere.';
$lang['form_validation_alpha_numeric_spaces']	= 'Polje {field} može sadržati samo alfa-numeričke karatkere i razmake.';
$lang['form_validation_alpha_dash']		= 'Polje {field} može sadržati samo alfa-numeričke karatkere, donje crte i srednje crte.';
$lang['form_validation_numeric']		= 'Polje {field} mora sadržati samo brojeve.';
$lang['form_validation_is_numeric']		= 'Polje {field} mora sadržati samo numeričke karaktere.';
$lang['form_validation_integer']		= 'Polje {field} mora sadržati bar jedan ceo broj.';
$lang['form_validation_regex_match']	= 'Polje {field} nije u ispravnom formatu.';
$lang['form_validation_matches']		= 'Polje {field} se ne podudara sa {param} poljem.';
$lang['form_validation_differs']		= 'Polje {field} mora biti različito od polja {param}.';
$lang['form_validation_is_unique'] 		= 'Polje {field} mora sadržati jedinstvenu vrednost.';
$lang['form_validation_is_natural']		= 'Polje {field} mora sadržati samo cifre.';
$lang['form_validation_is_natural_no_zero']	= 'Polje {field} mora sardžati samo cifre i mora biti veće od nule.';
$lang['form_validation_decimal']		= 'Polje {field} mora sadržati defimalni broj.';
$lang['form_validation_less_than']		= 'Polje {field} mora sadržati  broj manji od {param}.';
$lang['form_validation_less_than_equal_to']	= 'Polje {field} mora sardžati broj manji ili jednak od {param}.';
$lang['form_validation_greater_than']		= 'Polje {field} mora sadržati broj veći od {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Polje {field} mora sadržati broj jednak ili veći od {param}.';
$lang['form_validation_error_message_not_set']	= 'Ne može se pristupiti poruci o grešci koja odgovara imenu vašeg polja {field}.';
$lang['form_validation_in_list']		= 'Polje {field} mora biti jedno od: {param}.';
