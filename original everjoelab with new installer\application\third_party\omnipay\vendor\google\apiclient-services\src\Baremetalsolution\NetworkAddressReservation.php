<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Baremetalsolution;

class NetworkAddressReservation extends \Google\Model
{
  /**
   * @var string
   */
  public $endAddress;
  /**
   * @var string
   */
  public $note;
  /**
   * @var string
   */
  public $startAddress;

  /**
   * @param string
   */
  public function setEndAddress($endAddress)
  {
    $this->endAddress = $endAddress;
  }
  /**
   * @return string
   */
  public function getEndAddress()
  {
    return $this->endAddress;
  }
  /**
   * @param string
   */
  public function setNote($note)
  {
    $this->note = $note;
  }
  /**
   * @return string
   */
  public function getNote()
  {
    return $this->note;
  }
  /**
   * @param string
   */
  public function setStartAddress($startAddress)
  {
    $this->startAddress = $startAddress;
  }
  /**
   * @return string
   */
  public function getStartAddress()
  {
    return $this->startAddress;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(NetworkAddressReservation::class, 'Google_Service_Baremetalsolution_NetworkAddressReservation');
