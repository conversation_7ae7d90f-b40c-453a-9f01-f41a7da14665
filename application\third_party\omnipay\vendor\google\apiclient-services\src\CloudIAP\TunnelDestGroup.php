<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudIAP;

class TunnelDestGroup extends \Google\Collection
{
  protected $collection_key = 'fqdns';
  /**
   * @var string[]
   */
  public $cidrs;
  /**
   * @var string[]
   */
  public $fqdns;
  /**
   * @var string
   */
  public $name;

  /**
   * @param string[]
   */
  public function setCidrs($cidrs)
  {
    $this->cidrs = $cidrs;
  }
  /**
   * @return string[]
   */
  public function getCidrs()
  {
    return $this->cidrs;
  }
  /**
   * @param string[]
   */
  public function setFqdns($fqdns)
  {
    $this->fqdns = $fqdns;
  }
  /**
   * @return string[]
   */
  public function getFqdns()
  {
    return $this->fqdns;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TunnelDestGroup::class, 'Google_Service_CloudIAP_TunnelDestGroup');
