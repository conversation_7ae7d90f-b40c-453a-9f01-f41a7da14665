<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set']        = 'Nepodařilo se nalézt post proměnnou userfile.';
$lang['upload_file_exceeds_limit']      = 'The uploaded file exceeds the maximum allowed size in your PHP configuration file.';
$lang['upload_file_exceeds_limit']      = 'Překročena velikost souboru určená v php.ini.';
$lang['upload_file_exceeds_form_limit'] = 'Překročena velikost souboru určená formulářem.';
$lang['upload_file_partial']            = 'Soubor byl úspěšně nahrán.';
$lang['upload_no_temp_directory']       = '<PERSON>y<PERSON><PERSON> dočasný adresář.';
$lang['upload_unable_to_write_file']    = 'Soubor se nepodařilo zapsat na disk.';
$lang['upload_stopped_by_extension']    = 'Nahrávání souboru zastaveno rozšířením.';
$lang['upload_no_file_selected']        = 'Nebyl zvolen soubor k nahrání.';
$lang['upload_invalid_filetype']        = 'Tento typ souboru není dovoleno nahrát.';
$lang['upload_invalid_filesize']        = 'Velikost souboru je větší než povolená.';
$lang['upload_invalid_dimensions']      = 'Obrázek má jiné než povolené rozměry.';
$lang['upload_destination_error']       = 'Nastal problém při přesouvání souboru do cílové lokace.';
$lang['upload_no_filepath']             = 'Cesta pro upload je chybná.';
$lang['upload_no_file_types']           = 'Nejsou specifikovány žádné povolené typy souboru.';
$lang['upload_bad_filename']            = 'Jméno souboru na serveru již existuje.';
$lang['upload_not_writable']            = 'Do cesty pro upload se nepodařilo zapsat.';
