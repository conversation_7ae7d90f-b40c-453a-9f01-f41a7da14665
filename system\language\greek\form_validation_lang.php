<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Το {field} πεδίο απαιτείται.';
$lang['form_validation_isset']			= 'Το {field} πεδίο πρέπει να έχει μια τιμή.';
$lang['form_validation_valid_email']		= 'Το {field} πεδίο πρέπει να περιέχει ένα έγκυρο email.';
$lang['form_validation_valid_emails']		= 'Το {field} πεδίο πρέπει να περιέχει όλες τις διαυθύνσεις email έγκυρες.';
$lang['form_validation_valid_url']		= 'Το {field} πεδίο πρέπει να περιέχει ένα έγκυρο URL.';
$lang['form_validation_valid_ip']		= 'Το {field} πεδίο πρέπει να περιέχει μια έγκυρη IP.';
$lang['form_validation_min_length']		= 'Το {field} πεδίο πρέπει να περιέχει τουλάχιστον {param} χαρακτήρες.';
$lang['form_validation_max_length']		= 'Το {field} πεδίο δεν μπορεί να υπερβαίνει τους {param} χαρακτήρες.';
$lang['form_validation_exact_length']		= 'Το {field} πεδίο πρέπει να είναι ακριβώς {param} χαρακτήρες.';
$lang['form_validation_alpha']			= 'Το {field} πεδίο μπορεί να περιέχει μόνο αλφαβητικούς χαρακτήρες.';
$lang['form_validation_alpha_numeric']		= 'Το {field} πεδίο μπορεί να περιέχει μόνο αλφαριθμητικούς χαρακτήρες.';
$lang['form_validation_alpha_numeric_spaces']	= 'Το {field} πεδίο μπορεί να περιέχει μόνο αλφαριθμητικούς χαρακτήρες και κενά.';
$lang['form_validation_alpha_dash']		= 'Το {field} πεδίο μπορεί να περιέχει μόνο αλφαριθμητικούς χαρακτήρες, κάτω παύλες και παύλες.';
$lang['form_validation_numeric']		= 'Το {field} πεδίο πρέπει να περιέχει μόνο αριθμούς.';
$lang['form_validation_is_numeric']		= 'Το {field} πεδίο πρέπει να περιέχει μόνο αριθμητικούς χαρακτήρες.';
$lang['form_validation_integer']		= 'Το {field πρέπει να περιέχει έναν ακέραιο αριθμό.';
$lang['form_validation_regex_match']		= 'Το {field} πεδίο δεν είναι στη σωστή μορφή.';
$lang['form_validation_matches']		= 'Το {field} πεδίο δεν ταιριάζει με το {param} πεδίο.';
$lang['form_validation_differs']		= 'Το {field} πεδίο πρέπει να διαφέρει από το {param} πεδίο.';
$lang['form_validation_is_unique'] 		= 'Το {field} πεδίο πρέπει να περιέχει μια μοναδική τιμή.';
$lang['form_validation_is_natural']		= 'Το {field} πεδίο πρέπει να περιέχει μόνο ψηφία.';
$lang['form_validation_is_natural_no_zero']	= 'Το {field} πεδίο πρέπει να περιέχει μόνο ψηφία και πρέπει να είναι μεγαλύτερο από το μηδέν.';
$lang['form_validation_decimal']		= 'Το {field} πεδίο πρέπει να περιλαμβάνει ένα δεκαδικό αριθμό.';
$lang['form_validation_less_than']		= 'Το {field} πεδίο πρέπει να περιέχει έναν αριθμό μικρότερο από {param}.';
$lang['form_validation_less_than_equal_to']	= 'Το {field} πεδίο πρέπει να περιέχει έναν αριθμό μικρότερο ή ίσο με {param}.';
$lang['form_validation_greater_than']		= 'Το {field} πεδίο πρέπει να περιέχει έναν αριθμό μεγαλύτερο από το {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Το {field} πεδίο πρέπει να περιέχει ένα αριθμό μεγαλύτερο ή ίσο με {param}.';
$lang['form_validation_error_message_not_set']	= 'Δεν είναι δυνατή η πρόσβαση σε ένα μήνυμα σφάλματος που αντιστοιχεί στο πεδίο {field}.';
$lang['form_validation_in_list']		= 'Το {field} πεδίο πρέπει να περιέχει ένα απο: {param}.';
