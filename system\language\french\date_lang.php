<?php
/**
 * System messages translation for CodeIgniter(tm)
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year']     = "Année";
$lang['date_years']    = "Années";
$lang['date_month']    = "Mois";
$lang['date_months']   = "Mois";
$lang['date_week']     = "Semaine";
$lang['date_weeks']    = "Semaines";
$lang['date_day']      = "Jour";
$lang['date_days']     = "Jours";
$lang['date_hour']     = "Heure";
$lang['date_hours']    = "Heures";
$lang['date_minute']   = "Minute";
$lang['date_minutes']  = "Minutes";
$lang['date_second']   = "Seconde";
$lang['date_seconds']  = "Secondes";

$lang['UM12']    = "(UTC -12:00) Îles Baker / Howland";
$lang['UM11']    = "(UTC -11:00) Samoa fuseau horaire, Niue";
$lang['UM10']    = "(UTC -10:00) Hawaï et les Aléoutiennes, Îles Cook, Tahiti";
$lang['UM95']    = "(UTC -9:30) Îles Marquises";
$lang['UM9']     = "(UTC -9:00) Heure normale d'Alaska, Îles Gambier";
$lang['UM8']     = "(UTC -8:00) Heure du Pacifique";
$lang['UM7']     = "(UTC -7:00) Heure des Rocheuses";
$lang['UM6']     = "(UTC -6:00) Heure centrale";
$lang['UM5']     = "(UTC -5:00) Heure de l'Est, heure normale de l'Ouest des Caraïbes";
$lang['UM45']    = "(UTC -4:30) Heure normale vénézuélienne";
$lang['UM4']     = "(UTC -4:00) Heure de l'Atlantique, heure normale de l'Est des Caraïbes";
$lang['UM35']    = "(UTC -3:30) Heure normale de Terre-Neuve";
$lang['UM3']     = "(UTC -3:00) Argentine, Brésil, Guyane Française, Uruguay";
$lang['UM2']     = "(UTC -2:00) Géorgie du Sud/Îles Sandwich du Sud";
$lang['UM1']     = "(UTC -1:00) Açores, Cap-Vert";
$lang['UTC']     = "(UTC) Greenwich heure normale, heure d'Europe occidentale";
$lang['UP1']     = "(UTC +1:00) Heure d'Europe centrale, heure normale d'Afrique de l'Ouest";
$lang['UP2']     = "(UTC +2:00) Heure normale d'Afrique centrale, heure d'Europe orientale, heure normale de Kaliningrad";
$lang['UP3']     = "(UTC +3:00) Heure de Moscou, heure normale d'Afrique de l'Est";
$lang['UP35']    = "(UTC +3:30) Heure normale d'Iran";
$lang['UP4']     = "(UTC +4:00) Heure normale d'Azerbaïdjan, heure normale de Samara";
$lang['UP45']    = "(UTC +4:30) Heure normale d'Afghanistan";
$lang['UP5']     = "(UTC +5:00) Heure normale de Pakistan, Ekaterinbourg";
$lang['UP55']    = "(UTC +5:30) Heure normale d'Inde, heure Sri Lanka";
$lang['UP575']   = "(UTC +5:45) Heure Népal";
$lang['UP6']     = "(UTC +6:00) Heure Bangladesh, Bhoutan, Omsk";
$lang['UP65']    = "(UTC +6:30) Îles Cocos, Myanmar";
$lang['UP7']     = "(UTC +7:00) Heure Krasnoïarsk, Cambodge, Laos, Thaïlande, Viêt Nam";
$lang['UP8']     = "(UTC +8:00) Heure normale de l'Ouest de l'Australie, heure Beijing, Irkoutsk";
$lang['UP875']   = "(UTC +8:45) Heure normale de l'Ouest de l'Australie centrale";
$lang['UP9']     = "(UTC +9:00) Heure normale du Japon, heure normale de la Corée, heure Yakutsk";
$lang['UP95']    = "(UTC +9:30) Heure normale de l'Australie centrale";
$lang['UP10']    = "(UTC +10:00) Heure normale de l'Australie occidentale, heure Vladivostok";
$lang['UP105']   = "(UTC +10:30) L'île de Lord How";
$lang['UP11']    = "(UTC +11:00) Heure Magadan, heure Nouvelle-Calédonie, Îles Salomon";
$lang['UP115']   = "(UTC +11:30) Île Norfolk";
$lang['UP12']    = "(UTC +12:00) Heure Fidji, Îles Gilbert, Kamchatka, heure normale de la Nouvelle-Zélande";
$lang['UP1275']  = "(UTC +12:45) Heure normale des Îles Chatham";
$lang['UP13']    = "(UTC +13:00) Heure normale des Îles Phénix, heure Tonga";
$lang['UP14']    = "(UTC +14:00) Heure normale des Îles Line";
