<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-video-camera"></i> <?php echo $this->lang->line('video_conferences'); ?>
            <small><?php echo $this->lang->line('my_video_classes'); ?></small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="<?php echo base_url('user/user/dashboard'); ?>"><i class="fa fa-dashboard"></i> <?php echo $this->lang->line('dashboard'); ?></a></li>
            <li class="active"><?php echo $this->lang->line('video_conferences'); ?></li>
        </ol>
    </section>

    <section class="content">
        <?php if ($this->session->flashdata('msg')) { echo $this->session->flashdata('msg'); } ?>
        
        <!-- Quick Stats -->
        <div class="row">
            <div class="col-lg-4 col-xs-6">
                <div class="small-box bg-aqua">
                    <div class="inner">
                        <h3><?php echo count($upcoming); ?></h3>
                        <p><?php echo $this->lang->line('upcoming_classes'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-clock-o"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-xs-6">
                <div class="small-box bg-green">
                    <div class="inner">
                        <h3><?php echo count($ongoing); ?></h3>
                        <p><?php echo $this->lang->line('live_classes'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-play-circle"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-xs-6">
                <div class="small-box bg-yellow">
                    <div class="inner">
                        <h3><?php echo count($my_meetings); ?></h3>
                        <p><?php echo $this->lang->line('total_attended'); ?></p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Classes -->
        <?php if (!empty($ongoing)) { ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-play-circle text-green"></i> <?php echo $this->lang->line('live_classes'); ?></h3>
                        <div class="box-tools pull-right">
                            <span class="label label-success"><?php echo count($ongoing); ?> <?php echo $this->lang->line('active'); ?></span>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <?php foreach ($ongoing as $meeting) { ?>
                            <div class="col-md-6">
                                <div class="info-box bg-green">
                                    <span class="info-box-icon">
                                        <i class="fa fa-<?php echo $meeting->platform == 'zoom' ? 'video-camera' : ($meeting->platform == 'gmeet' ? 'google' : 'users'); ?>"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?php echo $meeting->title; ?></span>
                                        <span class="info-box-number"><?php echo $meeting->subject_name; ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 70%"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?php echo $meeting->host_name . ' ' . $meeting->host_surname; ?> • 
                                            <span class="label label-<?php echo $meeting->platform == 'zoom' ? 'primary' : ($meeting->platform == 'gmeet' ? 'success' : 'info'); ?>">
                                                <?php echo strtoupper($meeting->platform); ?>
                                            </span>
                                        </span>
                                        <div class="margin-top">
                                            <a href="<?php echo base_url('user/conference/join/' . $meeting->id); ?>" class="btn btn-success btn-sm">
                                                <i class="fa fa-play"></i> <?php echo $this->lang->line('join_now'); ?>
                                            </a>
                                            <a href="<?php echo base_url('user/conference/view/' . $meeting->id); ?>" class="btn btn-default btn-sm">
                                                <i class="fa fa-info-circle"></i> <?php echo $this->lang->line('details'); ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php } ?>

        <!-- Upcoming Classes -->
        <div class="row">
            <div class="col-md-8">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-calendar"></i> <?php echo $this->lang->line('upcoming_classes'); ?></h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo base_url('user/conference/timetable'); ?>" class="btn btn-default btn-xs">
                                <i class="fa fa-calendar"></i> <?php echo $this->lang->line('view_timetable'); ?>
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <?php if (!empty($upcoming)) { ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th><?php echo $this->lang->line('class'); ?></th>
                                            <th><?php echo $this->lang->line('subject'); ?></th>
                                            <th><?php echo $this->lang->line('time'); ?></th>
                                            <th><?php echo $this->lang->line('platform'); ?></th>
                                            <th><?php echo $this->lang->line('action'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($upcoming, 0, 5) as $meeting) { ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $meeting->title; ?></strong><br>
                                                <small class="text-muted"><?php echo $meeting->host_name . ' ' . $meeting->host_surname; ?></small>
                                            </td>
                                            <td><?php echo $meeting->subject_name ?: '-'; ?></td>
                                            <td>
                                                <?php echo date('M d, H:i', strtotime($meeting->start_time)); ?><br>
                                                <small class="text-muted"><?php echo $meeting->duration; ?> <?php echo $this->lang->line('minutes'); ?></small>
                                            </td>
                                            <td>
                                                <span class="label label-<?php echo $meeting->platform == 'zoom' ? 'primary' : ($meeting->platform == 'gmeet' ? 'success' : 'info'); ?>">
                                                    <?php echo strtoupper($meeting->platform); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo base_url('user/conference/view/' . $meeting->id); ?>" class="btn btn-info btn-xs">
                                                    <i class="fa fa-eye"></i> <?php echo $this->lang->line('view'); ?>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php if (count($upcoming) > 5) { ?>
                            <div class="text-center">
                                <a href="<?php echo base_url('user/conference/timetable'); ?>" class="btn btn-default">
                                    <?php echo $this->lang->line('view_all'); ?> (<?php echo count($upcoming); ?>)
                                </a>
                            </div>
                            <?php } ?>
                        <?php } else { ?>
                            <div class="text-center text-muted">
                                <i class="fa fa-calendar fa-3x"></i><br>
                                <p><?php echo $this->lang->line('no_upcoming_classes'); ?></p>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-md-4">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-bolt"></i> <?php echo $this->lang->line('quick_actions'); ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="list-group">
                            <a href="<?php echo base_url('user/conference/timetable'); ?>" class="list-group-item">
                                <i class="fa fa-calendar text-blue"></i> <?php echo $this->lang->line('view_timetable'); ?>
                                <span class="pull-right text-muted small">
                                    <i class="fa fa-chevron-right"></i>
                                </span>
                            </a>
                            <a href="<?php echo base_url('user/conference/history'); ?>" class="list-group-item">
                                <i class="fa fa-history text-green"></i> <?php echo $this->lang->line('class_history'); ?>
                                <span class="pull-right text-muted small">
                                    <i class="fa fa-chevron-right"></i>
                                </span>
                            </a>
                            <a href="<?php echo base_url('user/conference/recordings'); ?>" class="list-group-item">
                                <i class="fa fa-play-circle text-orange"></i> <?php echo $this->lang->line('recordings'); ?>
                                <span class="pull-right text-muted small">
                                    <i class="fa fa-chevron-right"></i>
                                </span>
                            </a>
                            <a href="<?php echo base_url('user/gmeet'); ?>" class="list-group-item">
                                <i class="fa fa-google text-red"></i> <?php echo $this->lang->line('google_meet'); ?>
                                <span class="pull-right text-muted small">
                                    <i class="fa fa-chevron-right"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Platform Status -->
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-cogs"></i> <?php echo $this->lang->line('platforms'); ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-xs-4 text-center">
                                <div class="small-box bg-blue">
                                    <div class="inner">
                                        <h4><i class="fa fa-video-camera"></i></h4>
                                        <p>Zoom</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-4 text-center">
                                <div class="small-box bg-green">
                                    <div class="inner">
                                        <h4><i class="fa fa-google"></i></h4>
                                        <p>Meet</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-4 text-center">
                                <div class="small-box bg-aqua">
                                    <div class="inner">
                                        <h4><i class="fa fa-users"></i></h4>
                                        <p>Jitsi</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <?php if (!empty($my_meetings)) { ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-history"></i> <?php echo $this->lang->line('recent_activity'); ?></h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo base_url('user/conference/history'); ?>" class="btn btn-default btn-xs">
                                <?php echo $this->lang->line('view_all'); ?>
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <ul class="timeline timeline-inverse">
                            <?php foreach (array_slice($my_meetings, 0, 5) as $meeting) { ?>
                            <li>
                                <i class="fa fa-video-camera bg-<?php echo $meeting->platform == 'zoom' ? 'blue' : ($meeting->platform == 'gmeet' ? 'green' : 'aqua'); ?>"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fa fa-clock-o"></i> <?php echo date('M d, H:i', strtotime($meeting->start_time)); ?></span>
                                    <h3 class="timeline-header"><?php echo $meeting->title; ?></h3>
                                    <div class="timeline-body">
                                        <strong><?php echo $meeting->subject_name; ?></strong> - <?php echo $meeting->host_name . ' ' . $meeting->host_surname; ?>
                                        <br>
                                        <span class="label label-<?php echo $meeting->participation_status == 'joined' ? 'success' : 'default'; ?>">
                                            <?php echo ucfirst($meeting->participation_status); ?>
                                        </span>
                                        <span class="label label-<?php echo $meeting->platform == 'zoom' ? 'primary' : ($meeting->platform == 'gmeet' ? 'success' : 'info'); ?>">
                                            <?php echo strtoupper($meeting->platform); ?>
                                        </span>
                                    </div>
                                </div>
                            </li>
                            <?php } ?>
                            <li>
                                <i class="fa fa-clock-o bg-gray"></i>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php } ?>
    </section>
</div>

<script>
$(document).ready(function() {
    // Auto-refresh ongoing meetings every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
});
</script>
