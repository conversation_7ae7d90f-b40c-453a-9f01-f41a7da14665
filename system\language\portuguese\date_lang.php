<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = 'Ano';
$lang['date_years'] = 'Anos';
$lang['date_month'] = 'Mês';
$lang['date_months'] = 'Meses';
$lang['date_week'] = 'Semana';
$lang['date_weeks'] = 'Semanas';
$lang['date_day'] = 'Dia';
$lang['date_days'] = 'Dias';
$lang['date_hour'] = 'Hora';
$lang['date_hours'] = 'Horas';
$lang['date_minute'] = 'Minuto';
$lang['date_minutes'] = 'Minutos';
$lang['date_second'] = 'Segundo';
$lang['date_seconds'] = 'Segundos';

$lang['UM12']   = '(UTC -12:00) Il<PERSON>/<PERSON>land';
$lang['UM11']   = '(UTC -11:00) Samoa Americana, Niue';
$lang['UM10']   = '(UTC -10:00) Hawai, Ilhas Cook e Aleutas';
$lang['UM95']   = '(UTC -9:30) Ilhas Marquesas';
$lang['UM9']    = '(UTC -9:00) Alaska, Ilhas Gambier';
$lang['UM8']    = '(UTC -8:00) Zona do pacífico, Ilha de Clipperton';
$lang['UM7']    = '(UTC -7:00) Zona das Montanhas';
$lang['UM6']    = '(UTC -6:00) Zona central da América do Norte';
$lang['UM5']    = '(UTC -5:00) Zona Leste Americana, Zona Oeste do Caribe';
$lang['UM45']   = '(UTC -4:30) Venezuela';
$lang['UM4']    = '(UTC -4:00) Zona do Atlântico, Zona Leste do Caribe';
$lang['UM35']   = '(UTC -3:30) Terra Nova(Canadá)';
$lang['UM3']    = '(UTC -3:00) Argentina, Brasil, Guiana Francesa, Uruguai';
$lang['UM2']    = '(UTC -2:00) Ilhas Georgia do Sul/Sandwich do Sul';
$lang['UM1']    = '(UTC -1:00) Açores, Cabo Verde';
$lang['UTC']    = '(UTC) Horário de Greenwich, Zona Oeste da Europa';
$lang['UP1']    = '(UTC +1:00) Zona Central da Europa, Zona Oeste de África';
$lang['UP2']    = '(UTC +2:00) Zona Central de África, Zona Leste da Europa, Zona de Kaliningrado';
$lang['UP3']    = '(UTC +3:00) Zona de Moscovo, Zona Leste de África';
$lang['UP35']   = '(UTC +3:30) Irão';
$lang['UP4']    = '(UTC +4:00) Azerbeijão, Zona da Samara';
$lang['UP45']   = '(UTC +4:30) Afeganistão';
$lang['UP5']    = '(UTC +5:00) Paquistão, Zona de Ecaterimburgo';
$lang['UP55']   = '(UTC +5:30) Índia, Sri Lanka';
$lang['UP575']  = '(UTC +5:45) Nepal';
$lang['UP6']    = '(UTC +6:00) Bangladesh, Butão, Zona de Omsk';
$lang['UP65']   = '(UTC +6:30) Ilhas Cocos, Mianmar';
$lang['UP7']    = '(UTC +7:00) Zona de Krasnoiarsk, Camboja, Laos, Tailândia, Vietnam';
$lang['UP8']    = '(UTC +8:00) Zona Oeste da Austrália, China, Zona de Irkutsk';
$lang['UP875']  = '(UTC +8:45) Zona Centro-Oeste da Austrália';
$lang['UP9']    = '(UTC +9:00) Japão, Coreia, Zona de Yakutsk';
$lang['UP95']   = '(UTC +9:30) Zona Central da Austrália';
$lang['UP10']   = '(UTC +10:00) Zona Leste da Austrália, Zona de Vladivostok';
$lang['UP105']  = '(UTC +10:30) Arquipélago Lord Howe';
$lang['UP11']   = '(UTC +11:00) Zona de Lacútia, Ilhas Salomão, Vanuatu';
$lang['UP115']  = '(UTC +11:30) Ilha Norfolk';
$lang['UP12']   = '(UTC +12:00) Fiji, Ilhas Gilbert, Zona de Kamchatka, Nova Zelândia';
$lang['UP1275'] = '(UTC +12:45) Ilhas Chatham';
$lang['UP13']   = '(UTC +13:00) Ilhas Fénix, Tonga';
$lang['UP14']   = '(UTC +14:00) Ilhas da linha';

