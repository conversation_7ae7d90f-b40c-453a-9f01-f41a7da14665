<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = '无法找到用户文件。  ';
$lang['upload_file_exceeds_limit'] = '上传文件的大小超过 PHP 设置中指定的最大大小。';
$lang['upload_file_exceeds_form_limit'] = '上传文件的大小超过表单中指定的最大大小。';
$lang['upload_file_partial'] = '文件仅上传了一部分。  .';
$lang['upload_no_temp_directory'] = '无法找到临时文件夹。  .';
$lang['upload_unable_to_write_file'] = '无法写入文件。.';
$lang['upload_stopped_by_extension'] = '文件上传被扩展停止。  ';
$lang['upload_no_file_selected'] = '没有选择要上传的文件。  ';
$lang['upload_invalid_filetype'] = '禁止上传的文件类型.  ';
$lang['upload_invalid_filesize'] = '文件大小超过限制。';
$lang['upload_invalid_dimensions'] = '不允许的图像尺寸。';
$lang['upload_destination_error'] = '移动上传的文件至最终保存路径时发生错误。  ';
$lang['upload_no_filepath'] = '上传路径无效。';
$lang['upload_no_file_types'] = '指定允许的文件类型。';
$lang['upload_bad_filename'] = '提交的文件名已经存在。';
$lang['upload_not_writable'] = '上传的目的路径不可写。';
