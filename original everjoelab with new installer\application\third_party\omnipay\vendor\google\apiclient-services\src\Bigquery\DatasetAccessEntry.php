<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Bigquery;

class DatasetAccessEntry extends \Google\Collection
{
  protected $collection_key = 'targetTypes';
  protected $datasetType = DatasetReference::class;
  protected $datasetDataType = '';
  /**
   * @var string[]
   */
  public $targetTypes;

  /**
   * @param DatasetReference
   */
  public function setDataset(DatasetReference $dataset)
  {
    $this->dataset = $dataset;
  }
  /**
   * @return DatasetReference
   */
  public function getDataset()
  {
    return $this->dataset;
  }
  /**
   * @param string[]
   */
  public function setTargetTypes($targetTypes)
  {
    $this->targetTypes = $targetTypes;
  }
  /**
   * @return string[]
   */
  public function getTargetTypes()
  {
    return $this->targetTypes;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DatasetAccessEntry::class, 'Google_Service_Bigquery_DatasetAccessEntry');
