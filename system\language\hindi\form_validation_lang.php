<?php
/**
* System messages translation for CodeIgniter(tm)
*
* <AUTHOR> community
* @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
* @license	http://opensource.org/licenses/MIT MIT License
* @link	https://codeigniter.com
*/
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= '{field} फ़ील्ड आवश्यक है।';
$lang['form_validation_isset']			= '{field} फ़ील्ड में वैल्यू आवश्यक है।';
$lang['form_validation_valid_email']		= '{field} फ़ील्ड एक सही ईमेल पता होना चाहिए।';
$lang['form_validation_valid_emails']		= '{field} फ़ील्ड में सारे सही ईमेल एड्रेस होने चाहिए।';
$lang['form_validation_valid_url']		= '{field} फील्ड में वैध यूआरएल होना चाहिए।';
$lang['form_validation_valid_ip']		= '{field} फ़ील्ड एक सही यूआरएल होनी  चाहिए।';
$lang['form_validation_min_length']		= '{field} फील्ड लंबाई में कम से कम {param} अक्षरों का होना चाहिए।';
$lang['form_validation_max_length']		= '{field} फील्ड लंबाई में  {param} अक्षरोंसे ज्यादा नहीं होना चाहिए।';
$lang['form_validation_exact_length']		= '{field} फील्ड लंबाई में बराबर  {param} अक्षरही होना चाहिए।';
$lang['form_validation_alpha']			= '{field} फील्ड केवल वर्णमाला वर्ण (alphabetic characters) हो सकते हैं।';
$lang['form_validation_alpha_numeric']		= '{field} फील्ड केवल अल्फा-न्यूमेरिक वर्ण हो सकते हैं।';
$lang['form_validation_alpha_numeric_spaces']	= '{field} फील्ड केवल अल्फा-न्यूमेरिक वर्ण और रिक्त स्थान हो सकता है।';
$lang['form_validation_alpha_dash']		= '{field} फील्ड केवल अल्फा-न्यूमेरिक वर्ण, अंडरस्कोर और डैश शामिल कर सकते हैं।';
$lang['form_validation_numeric']		= '{field} फील्ड केवल अंक ही होना चाहिए।';
$lang['form_validation_is_numeric']		= '{field} फील्ड में केवल संख्यात्मक अक्षर शामिल होना चाहिए।';
$lang['form_validation_integer']		= '{field} फील्ड एक पूर्णांक होना चाहिए।';
$lang['form_validation_regex_match']		= '{field} फील्ड सही स्वरूप में नहीं है।';
$lang['form_validation_matches']		= '{field} फील्ड {param} फील्ड से मेल नहीं खाती।';
$lang['form_validation_differs']		= '{field} फील्ड {param} फील्ड से अलग होनी चाहिए।';
$lang['form_validation_is_unique'] 		= '{field} फील्ड एक अनूठा मूल्य होना चाहिए।';
$lang['form_validation_is_natural']		= '{field} फील्ड में केवल अंक होने चाहिए।';
$lang['form_validation_is_natural_no_zero']	= '{field} फील्ड में केवल अंक होने चाहिए और शून्य से अधिक होना चाहिए।';
$lang['form_validation_decimal']		= '{field} फील्ड में दशमलव संख्या में होना चाहिए।';
$lang['form_validation_less_than']		= '{field} फील्ड {param} से कम होना चाहिए।';
$lang['form_validation_less_than_equal_to']	= '{field} फील्ड {param} से कम या बराबर होना चाहिए।';
$lang['form_validation_greater_than']		= '{field} फील्ड {param} से बड़ा अंक होने चाहिए।';
$lang['form_validation_greater_than_equal_to']	= '{field} फील्ड {param} से अधिक या बराबर होना चाहिए।';
$lang['form_validation_error_message_not_set']	= '{field} फील्ड से सम्बंधित त्रुटि संदेश का उपयोग करने में असमर् थ।';
$lang['form_validation_in_list']		= '{field} फील्ड {param} में से एक होनी चाहिए।';
