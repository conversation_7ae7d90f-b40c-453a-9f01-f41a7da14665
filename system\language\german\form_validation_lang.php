<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Das {field}-Formularfeld ist eine Pflichtangabe.';
$lang['form_validation_isset']			= 'Das {field}-Formularfeld muss einen gültigen Wert enthalten.';
$lang['form_validation_valid_email']		= 'Das {field}-Formularfeld muss eine gültige E-Mail-Adresse beinhalten.';
$lang['form_validation_valid_emails']		= 'Das {field}-Formularfeld muss gültige E-Mail-Adressen beinhalten.';
$lang['form_validation_valid_url']		= 'Das {field}-Formularfeld muss eine gültige URL beinhalten.';
$lang['form_validation_valid_ip']		= 'Das {field}-Formularfeld muss eine gültige IP-Adresse beinhalten.';
$lang['form_validation_min_length']		= 'Das {field}-Formularfeld muss mindestens {param} Zeichen lang sein.';
$lang['form_validation_max_length']		= 'Das {field}-Formularfeld darf nicht mehr als {param} Zeichen lang sein.';
$lang['form_validation_exact_length']		= 'Das {field}-Formularfeld muss exakt {param} Zeichen lang sein.';
$lang['form_validation_alpha']			= 'Das {field}-Formularfeld darf nur alphabetische Zeichen enthalten.';
$lang['form_validation_alpha_numeric']		= 'Das {field}-Formularfeld darf nur alphanumerische Zeichen enthalten.';
$lang['form_validation_alpha_numeric_spaces']	= 'Das {field}-Formularfeld darf nur alphanumerische Zeichen und Leerzeichen enthalten.';
$lang['form_validation_alpha_dash']		= 'Das {field}-Formularfeld darf nur alphanumerische Zeichen, Unterstriche und Bindestriche enthalten.';
$lang['form_validation_numeric']		= 'Das {field}-Formularfeld darf nur Zahlen enthalten.';
$lang['form_validation_is_numeric']		= 'Das {field}-Formularfeld darf nur numerische Zeichen enthalten.';
$lang['form_validation_integer']		= 'Das {field}-Formularfeld muss einen Integer-Wert enthalten.';
$lang['form_validation_regex_match']		= 'Das {field}-Formularfeld hat keinen gültigen Wert.';
$lang['form_validation_matches']		= 'Das {field}-Formularfeld stimmt nicht mit dem {param} Formularfeld überein.';
$lang['form_validation_differs']		= 'Das {field}-Formularfeld muss sich vom {param} Formularfeld unterscheiden.';
$lang['form_validation_is_unique'] 		= 'Das {field}-Formularfeld muss einen eindeutigen (unique) Wert enthalten.';
$lang['form_validation_is_natural']		= 'Das {field}-Formularfeld muss Zahlen enthalten.';
$lang['form_validation_is_natural_no_zero']	= 'Das {field}-Formularfeld muss Zahlen enthalten und größer als Null sein.';
$lang['form_validation_decimal']		= 'Das {field}-Formularfeld mus eine Dezimalzahl enthalten.';
$lang['form_validation_less_than']		= 'Das {field}-Formularfeld muss einen Zahlenwert enthalten der kleiner als {param} ist.';
$lang['form_validation_less_than_equal_to']	= 'Das {field}-Formularfeld muss einen Zahlenwert enthalten der kleiner oder gleich {param} ist.';
$lang['form_validation_greater_than']		= 'Das {field}-Formularfeld muss einen Zahlenwert enthalten der gößer als {param} ist.';
$lang['form_validation_greater_than_equal_to']	= 'Das {field}-Formularfeld muss einen Wert enthalten, der größer oder gleich {param} ist.';
$lang['form_validation_error_message_not_set']	= 'Kann auf keine Fehlermeldung zum Feldnamen "{field}" zugreifen.';
$lang['form_validation_in_list']		= 'Das {field}-Formularfeld muss eins dieser Elemente enthalten: {param}.';
