<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['migration_none_found'] = 'Не было найдено ни одной миграции.';
$lang['migration_not_found'] = 'Не было найдено ни одной миграции с версией: %s.';
$lang['migration_sequence_gap'] = 'Обнаружен разрыв в последовательности рядом с версией: %s.';
$lang['migration_multiple_version'] = 'Имеется несколько миграций с одной версией: %s.';
$lang['migration_class_doesnt_exist'] = 'Невозможно найти класс миграции "%s".';
$lang['migration_missing_up_method'] = 'Класс миграции "%s" не содержит "up" метода.';
$lang['migration_missing_down_method'] = 'Класс миграции "%s" не содержит "down" метода.';
$lang['migration_invalid_filename'] = 'Миграция "%s" имеет недопустимое имя файла.';
