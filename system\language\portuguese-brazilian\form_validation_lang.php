<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = 'O campo {field} é obrigatório.';
$lang['form_validation_isset'] = 'O campo {field} deve conter um valor.';
$lang['form_validation_valid_email'] = 'O campo {field} deve conter um email válido.';
$lang['form_validation_valid_emails'] = 'O campo {field} deve conter apenas emails válidos.';
$lang['form_validation_valid_url'] = 'O campo {field} deve conter uma URL válida.';
$lang['form_validation_valid_ip'] = 'O campo {field} deve conter um IP válido.';
$lang['form_validation_min_length'] = 'O campo {field} deve ter pelo menos {param} caractere(s).';
$lang['form_validation_max_length'] = 'O campo {field} ultrapassou o limite de {param} caractere(s).';
$lang['form_validation_exact_length'] = 'O campo {field} deve conter exatamente {param} caractere(s).';
$lang['form_validation_alpha'] = 'O campo {field} deve conter somente letras.';
$lang['form_validation_alpha_numeric'] = 'O campo {field} deve conter somente letras e números.';
$lang['form_validation_alpha_numeric_spaces'] = 'O campo {field} deve conter somente letras, números e espaços.';
$lang['form_validation_alpha_dash'] = 'O campo {field} deve conter somente letras, números, sublinhados e traços.';
$lang['form_validation_numeric'] = 'O campo {field} deve conter somente números.';
$lang['form_validation_is_numeric'] = 'O campo {field} deve conter somente números.';
$lang['form_validation_integer'] = 'O campo {field} deve conter um número inteiro.';
$lang['form_validation_regex_match'] = 'O campo {field} não está em um formato correto.';
$lang['form_validation_matches'] = 'O campo {field} não é igual ao campo {param}.';
$lang['form_validation_differs'] = 'O campo {field} deve ser diferente do campo {param}.';
$lang['form_validation_is_unique'] = 'O campo {field} já existe, ele deve ser único.';
$lang['form_validation_is_natural'] = 'O campo {field} deve conter um número natural.';
$lang['form_validation_is_natural_no_zero'] = 'O campo {field} deve conter um número natural diferente de zero.';
$lang['form_validation_decimal'] = 'O campo {field} deve conter um número decimal.';
$lang['form_validation_less_than'] = 'O campo {field} deve conter um número menor que {param}';
$lang['form_validation_less_than_equal_to'] = 'O campo {field} deve conter um número menor ou igual que {param}.';
$lang['form_validation_greater_than'] = 'O campo {field} deve conter um número maior que {param}.';
$lang['form_validation_greater_than_equal_to'] = 'O campo {field} deve conter um número maior ou igual que {param}.';
$lang['form_validation_error_message_not_set'] = 'Não existe uma mensagem de erro para o campo com o nome {field}.';
$lang['form_validation_in_list'] = 'O campo {field} deve ser um de: {param}.';
