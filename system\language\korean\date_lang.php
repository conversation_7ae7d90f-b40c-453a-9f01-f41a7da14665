<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license http://opensource.org/licenses/MIT MIT License
 * @link https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = '년';
$lang['date_years'] = '년';
$lang['date_month'] = '월';
$lang['date_months'] = '월';
$lang['date_week'] = '주';
$lang['date_weeks'] = '주';
$lang['date_day'] = '요일';
$lang['date_days'] = '요일';
$lang['date_hour'] = '시';
$lang['date_hours'] = '시';
$lang['date_minute'] = '분';
$lang['date_minutes'] = '분';
$lang['date_second'] = '초';
$lang['date_seconds'] = '초';

$lang['UM12']	= '(UTC -12:00) 베이커 / 하울랜드 섬';
$lang['UM11']	= '(UTC -11:00) 니우에';
$lang['UM10']	= '(UTC -10:00) 하와이-알류샨 표준시, 쿡 제도, 타히티';
$lang['UM95']	= '(UTC -9:30) 마르키즈 제도';
$lang['UM9']	= '(UTC -9:00) 알래스카 표준시, 갬비어 제도';
$lang['UM8']	= '(UTC -8:00) 태평양 표준시, 클리퍼턴 섬';
$lang['UM7']	= '(UTC -7:00) 아메리카 산지 표준시';
$lang['UM6']	= '(UTC -6:00) 중앙 아메리카 표준시';
$lang['UM5']	= '(UTC -5:00) 동부 아메리카 표준시, 서부 카리브해 표준시';
$lang['UM45']	= '(UTC -4:30) 베네수엘라 표준시';
$lang['UM4']	= '(UTC -4:00) 대서양 표준시, 동부 카리브해 표준시';
$lang['UM35']	= '(UTC -3:30) 뉴펀들랜드 표준시';
$lang['UM3']	= '(UTC -3:00) 아르헨티나, 브라질, 프랑스령 기아나, 우루과이';
$lang['UM2']	= '(UTC -2:00) 사우스 조지아 / 사우스 샌드위치 제도';
$lang['UM1']	= '(UTC -1:00) 아소르스 제도, 카보베르데';
$lang['UTC']	= '(UTC) 그리니치 표준시, 서부 유럽 표준시';
$lang['UP1']	= '(UTC +1:00) 중앙 유럽 표준시, 서 아프리카 시간';
$lang['UP2']	= '(UTC +2:00) 중앙 아프리카 시간, 동유럽 시간, 칼리닌그라드 시간';
$lang['UP3']	= '(UTC +3:00) 모스크바 시간, 동부 아프리카 시간, 아라비아 표준시';
$lang['UP35']	= '(UTC +3:30) 이란 표준시';
$lang['UP4']	= '(UTC +4:00) 아제르바이잔 표준시, 사마라 시간';
$lang['UP45']	= '(UTC +4:30) 아프가니스탄';
$lang['UP5']	= '(UTC +5:00) 파키스탄 표준시, 예카테린부르크 시간';
$lang['UP55']	= '(UTC +5:30) 인도 표준시, 스리랑카 시간';
$lang['UP575']	= '(UTC +5:45) 네팔 시간';
$lang['UP6']	= '(UTC +6:00) 방글라데시 표준시, 부탄 시간, 옴스크 시간';
$lang['UP65']	= '(UTC +6:30) 코코스 아일랜드, 미얀마';
$lang['UP7']	= '(UTC +7:00) 크라스노야르스크 시간, 캄보디아, 라오스, 태국, 베트남';
$lang['UP8']	= '(UTC +8:00) 호주 서부 표준시, 베이징 시간, 이르쿠츠크 시간';
$lang['UP875']	= '(UTC +8:45) 호주 중앙 서부 표준시';
$lang['UP9']	= '(UTC +9:00) 한국 표준시, 일본 표준시, 야쿠츠크 시간';
$lang['UP95']	= '(UTC +9:30) 호주 중부 표준시';
$lang['UP10']	= '(UTC +10:00) 호주 동부 표준시, 블라디보스톡 시간';
$lang['UP105']	= '(UTC +10:30) 로드 하우 섬';
$lang['UP11']	= '(UTC +11:00) 스레드네콜림스크 시간, 솔로몬 제도, 바누아투';
$lang['UP115']	= '(UTC +11:30) 노퍽 섬';
$lang['UP12']	= '(UTC +12:00) 피지, 길버트 제도, 캄차카 시간, 뉴질랜드 표준시';
$lang['UP1275']	= '(UTC +12:45) 채텀 제도 표준시';
$lang['UP13']	= '(UTC +13:00) 사모아 시간대, 피닉스 제도 시간, 통가';
$lang['UP14']	= '(UTC +14:00) 라인제도';
