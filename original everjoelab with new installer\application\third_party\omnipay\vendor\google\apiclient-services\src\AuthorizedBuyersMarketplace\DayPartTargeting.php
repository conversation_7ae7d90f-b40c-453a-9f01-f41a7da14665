<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AuthorizedBuyersMarketplace;

class DayPartTargeting extends \Google\Collection
{
  protected $collection_key = 'dayParts';
  protected $dayPartsType = DayPart::class;
  protected $dayPartsDataType = 'array';
  /**
   * @var string
   */
  public $timeZoneType;

  /**
   * @param DayPart[]
   */
  public function setDayParts($dayParts)
  {
    $this->dayParts = $dayParts;
  }
  /**
   * @return DayPart[]
   */
  public function getDayParts()
  {
    return $this->dayParts;
  }
  /**
   * @param string
   */
  public function setTimeZoneType($timeZoneType)
  {
    $this->timeZoneType = $timeZoneType;
  }
  /**
   * @return string
   */
  public function getTimeZoneType()
  {
    return $this->timeZoneType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DayPartTargeting::class, 'Google_Service_AuthorizedBuyersMarketplace_DayPartTargeting');
