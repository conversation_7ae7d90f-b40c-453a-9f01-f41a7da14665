<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['imglib_source_image_required'] = '必須在設定中指定一個來源圖檔';
$lang['imglib_gd_required'] = '此功能依賴 GD 圖形函式庫';
$lang['imglib_gd_required_for_props'] = '您的伺服器必須支援 GD 圖形函式庫以確定圖檔的屬性';
$lang['imglib_unsupported_imagecreate'] = '您的伺服器不支援 GD 函數，無法建立影像檔';
$lang['imglib_gif_not_supported'] = 'GIF 格式由於版權問題一般不提供，建議使用 JPG 或 PNG 格式';
$lang['imglib_jpg_not_supported'] = '不支援 JPG 格式';
$lang['imglib_png_not_supported'] = '不支援 PNG 格式';
$lang['imglib_jpg_or_png_required'] = '設定中指定的圖檔縮放方法只能用於 JPG 或 PNG 格式';
$lang['imglib_copy_error'] = '取代檔案時發生錯誤。請確認檔案目錄可寫入';
$lang['imglib_rotate_unsupported'] = '伺服器不支援圖檔轉向';
$lang['imglib_libpath_invalid'] = '圖形函式庫路徑錯誤。在設定中必須指定正確的路徑';
$lang['imglib_image_process_failed'] = '影像處理錯誤。請確認伺服器支援指定的處理方法，且圖形函式庫徑正確';
$lang['imglib_rotation_angle_required'] = '必須指定旋轉角度';
$lang['imglib_invalid_path'] = '圖檔路徑錯誤';
$lang['imglib_invalid_image'] = '無效的圖檔';
$lang['imglib_copy_failed'] = '圖檔複製錯誤';
$lang['imglib_missing_font'] = '無法找到指定的字體';
$lang['imglib_save_failed'] = '無法存檔，請確定圖檔或目錄可寫';
