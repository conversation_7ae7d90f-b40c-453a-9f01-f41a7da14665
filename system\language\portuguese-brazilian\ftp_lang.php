<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ftp_no_connection'] = 'Não foi possível localizar um ID de conexão válido. Por favor tenha certeza que você está conectado antes de executar qualquer arquivo de rotina.';
$lang['ftp_unable_to_connect'] = 'Não foi possível conectar ao seu servidor FTP usando o endereço fornecido.';
$lang['ftp_unable_to_login'] = 'Não foi possível fazer login em seu servidor FTP. Por favor verifique seu usuário e senha.';
$lang['ftp_unable_to_mkdir'] = 'Não foi possível criar o diretório que você especificou.';
$lang['ftp_unable_to_changedir'] = 'Não foi possível mundar diretórios.';
$lang['ftp_unable_to_chmod'] = 'Não foi possível configurar permissões de arquivo. Por favor verifique o seu caminho.';
$lang['ftp_unable_to_upload'] = 'Não foi possível fazer upload do arquivo especificado. Por favor verifique seu caminho.';
$lang['ftp_unable_to_download'] = 'Não foi possível fazer download do arquivo especificado. Por favor verifique seu caminho.';
$lang['ftp_no_source_file'] = 'Não foi possível localizar o arquivo de origem. Por favor verifique seu caminho.';
$lang['ftp_unable_to_rename'] = 'Não foi possível renomear o arquivo.';
$lang['ftp_unable_to_delete'] = 'Não foi possível remover o arquivo.';
$lang['ftp_unable_to_move'] = 'Não foi possível mover o arquivo. Por favor tenha certeza que o diretório destino existe.';
