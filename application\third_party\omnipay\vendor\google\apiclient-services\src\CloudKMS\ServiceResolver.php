<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudKMS;

class ServiceResolver extends \Google\Collection
{
  protected $collection_key = 'serverCertificates';
  /**
   * @var string
   */
  public $endpointFilter;
  /**
   * @var string
   */
  public $hostname;
  protected $serverCertificatesType = Certificate::class;
  protected $serverCertificatesDataType = 'array';
  /**
   * @var string
   */
  public $serviceDirectoryService;

  /**
   * @param string
   */
  public function setEndpointFilter($endpointFilter)
  {
    $this->endpointFilter = $endpointFilter;
  }
  /**
   * @return string
   */
  public function getEndpointFilter()
  {
    return $this->endpointFilter;
  }
  /**
   * @param string
   */
  public function setHostname($hostname)
  {
    $this->hostname = $hostname;
  }
  /**
   * @return string
   */
  public function getHostname()
  {
    return $this->hostname;
  }
  /**
   * @param Certificate[]
   */
  public function setServerCertificates($serverCertificates)
  {
    $this->serverCertificates = $serverCertificates;
  }
  /**
   * @return Certificate[]
   */
  public function getServerCertificates()
  {
    return $this->serverCertificates;
  }
  /**
   * @param string
   */
  public function setServiceDirectoryService($serviceDirectoryService)
  {
    $this->serviceDirectoryService = $serviceDirectoryService;
  }
  /**
   * @return string
   */
  public function getServiceDirectoryService()
  {
    return $this->serviceDirectoryService;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ServiceResolver::class, 'Google_Service_CloudKMS_ServiceResolver');
