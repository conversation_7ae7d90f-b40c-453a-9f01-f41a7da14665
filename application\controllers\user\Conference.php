<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Conference extends Student_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model(array('video_conference_model', 'video_conference_settings_model'));
        $this->load->library(array('zoom_lib', 'gmeet_lib', 'jitsi_lib'));
    }

    public function index()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);

        $data['title'] = 'Video Conferences';
        $data['upcoming'] = $this->video_conference_model->getUpcoming(10);
        $data['ongoing'] = $this->video_conference_model->getOngoing();
        $data['my_meetings'] = $this->video_conference_model->getStudentMeetings($student_id, 10);
        
        // Filter meetings for student's class
        if ($student) {
            $class_meetings = $this->video_conference_model->getByClass($student->class_id, $student->section_id);
            $data['class_meetings'] = $class_meetings;
        } else {
            $data['class_meetings'] = array();
        }

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/conference/index', $data);
        $this->load->view('layout/student/footer', $data);
    }

    public function join($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        $conference = $this->video_conference_model->get($id);

        if (!$conference) {
            show_404();
        }

        // Check if student is allowed to join this meeting
        if (!$this->_can_join_meeting($conference, $student)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">You are not authorized to join this meeting!</div>');
            redirect('user/conference');
        }

        // Check meeting timing
        $current_time = time();
        $start_time = strtotime($conference->start_time);
        $end_time = strtotime($conference->end_time);
        $join_before_time = $start_time - (15 * 60); // Allow joining 15 minutes before

        if ($current_time < $join_before_time) {
            $this->session->set_flashdata('msg', '<div class="alert alert-warning">Meeting has not started yet. You can join 15 minutes before the scheduled time.</div>');
            redirect('user/conference');
        }

        if ($current_time > $end_time) {
            $this->session->set_flashdata('msg', '<div class="alert alert-info">This meeting has already ended.</div>');
            redirect('user/conference');
        }

        // Log participant join
        $participant_data = array(
            'participant_type' => 'student',
            'participant_id' => $student_id,
            'join_time' => date('Y-m-d H:i:s'),
            'status' => 'joined'
        );

        // Check if participant already exists
        $participants = $this->video_conference_model->getParticipants($id);
        $participant_exists = false;
        foreach ($participants as $participant) {
            if ($participant->participant_id == $student_id && $participant->participant_type == 'student') {
                $participant_exists = true;
                // Update join time
                $this->video_conference_model->updateParticipantStatus($id, $student_id, 'student', $participant_data);
                break;
            }
        }

        if (!$participant_exists) {
            $this->video_conference_model->addParticipant($id, $participant_data);
        }

        // Update meeting status if needed
        if ($conference->status == 'scheduled' && $current_time >= $start_time) {
            $this->video_conference_model->updateStatus($id, 'ongoing');
        }

        // Generate platform-specific join URL
        $join_url = $this->_get_join_url($conference, $student);

        if ($join_url) {
            redirect($join_url);
        } else {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger">Unable to generate meeting URL!</div>');
            redirect('user/conference');
        }
    }

    private function _can_join_meeting($conference, $student)
    {
        // Check if meeting is for student's class
        if ($conference->class_id && $conference->section_id) {
            return ($conference->class_id == $student->class_id && $conference->section_id == $student->section_id);
        }

        // Check if student is explicitly invited
        $participants = $this->video_conference_model->getParticipants($conference->id);
        foreach ($participants as $participant) {
            if ($participant->participant_id == $student->id && $participant->participant_type == 'student') {
                return true;
            }
        }

        // If no class restriction and no explicit invitation, allow all students
        if (!$conference->class_id && !$conference->section_id) {
            return true;
        }

        return false;
    }

    private function _get_join_url($conference, $student)
    {
        switch ($conference->platform) {
            case 'zoom':
                return $conference->meeting_url;
            
            case 'gmeet':
                return $conference->meeting_url;
            
            case 'jitsi':
                // For Jitsi, we can customize the join URL with user info
                $url = $conference->meeting_url;
                $params = array(
                    'userInfo.displayName' => $student->firstname . ' ' . $student->lastname,
                    'userInfo.email' => $student->email ?: ''
                );
                
                if (strpos($url, '?') !== false) {
                    $url .= '&' . http_build_query($params);
                } else {
                    $url .= '?' . http_build_query($params);
                }
                
                return $url;
            
            default:
                return $conference->meeting_url;
        }
    }

    public function view($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        $conference = $this->video_conference_model->get($id);

        if (!$conference) {
            show_404();
        }

        // Check if student can view this meeting
        if (!$this->_can_join_meeting($conference, $student)) {
            show_404();
        }

        $data['title'] = 'Conference Details';
        $data['conference'] = $conference;
        $data['can_join'] = $this->_can_join_now($conference);
        $data['participants'] = $this->video_conference_model->getParticipants($id);

        // Generate embed code for Jitsi meetings
        if ($conference->platform == 'jitsi') {
            $data['jitsi_embed'] = $this->jitsi_lib->generateJavaScriptAPI($conference->meeting_id);
            $data['jitsi_iframe'] = $this->jitsi_lib->generateIframeCode($conference->meeting_id);
        }

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/conference/view', $data);
        $this->load->view('layout/student/footer', $data);
    }

    private function _can_join_now($conference)
    {
        $current_time = time();
        $start_time = strtotime($conference->start_time);
        $end_time = strtotime($conference->end_time);
        $join_before_time = $start_time - (15 * 60); // Allow joining 15 minutes before

        return ($current_time >= $join_before_time && $current_time <= $end_time && $conference->status != 'cancelled');
    }

    public function leave($id)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $conference = $this->video_conference_model->get($id);

        if (!$conference) {
            show_404();
        }

        // Update participant leave time
        $participant_data = array(
            'leave_time' => date('Y-m-d H:i:s'),
            'status' => 'left'
        );

        $this->video_conference_model->updateParticipantStatus($id, $student_id, 'student', $participant_data);

        $this->session->set_flashdata('msg', '<div class="alert alert-success">You have left the meeting.</div>');
        redirect('user/conference');
    }

    public function timetable()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);

        $data['title'] = 'Conference Timetable';
        
        // Get conferences for the next 7 days
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime('+7 days'));
        
        $data['conferences'] = array();
        if ($student) {
            $class_meetings = $this->video_conference_model->getByClass($student->class_id, $student->section_id);
            
            // Filter by date range
            foreach ($class_meetings as $meeting) {
                $meeting_date = date('Y-m-d', strtotime($meeting->start_time));
                if ($meeting_date >= $start_date && $meeting_date <= $end_date) {
                    $data['conferences'][] = $meeting;
                }
            }
        }

        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/conference/timetable', $data);
        $this->load->view('layout/student/footer', $data);
    }

    public function history()
    {
        $student_id = $this->customlib->getStudentSessionUserID();

        $data['title'] = 'Conference History';
        $data['meetings'] = $this->video_conference_model->getStudentMeetings($student_id, 50);

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/conference/history', $data);
        $this->load->view('layout/student/footer', $data);
    }

    public function recordings($id = null)
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);

        $data['title'] = 'Conference Recordings';
        $data['recordings'] = array();

        if ($id) {
            $conference = $this->video_conference_model->get($id);
            if ($conference && $this->_can_join_meeting($conference, $student)) {
                // Get recordings for specific meeting
                switch ($conference->platform) {
                    case 'zoom':
                        $result = $this->zoom_lib->getRecordings($conference->meeting_id);
                        if (isset($result['success']) && $result['success']) {
                            $data['recordings'] = $result['recordings'];
                        }
                        break;
                    // Google Meet and Jitsi recordings would need additional implementation
                }
                $data['conference'] = $conference;
            }
        } else {
            // Get all recordings for student's meetings
            $meetings = $this->video_conference_model->getStudentMeetings($student_id, 100);
            foreach ($meetings as $meeting) {
                if ($meeting->is_recorded && $meeting->platform == 'zoom') {
                    $result = $this->zoom_lib->getRecordings($meeting->meeting_id);
                    if (isset($result['success']) && $result['success']) {
                        foreach ($result['recordings'] as $recording) {
                            $recording['meeting_title'] = $meeting->title;
                            $recording['meeting_date'] = $meeting->start_time;
                            $data['recordings'][] = $recording;
                        }
                    }
                }
            }
        }

        $this->load->view('layout/student/header', $data);
        $this->load->view('user/conference/recordings', $data);
        $this->load->view('layout/student/footer', $data);
    }

    public function ajax_get_meetings()
    {
        $student_id = $this->customlib->getStudentSessionUserID();
        $student = $this->student_model->get($student_id);
        
        $date = $this->input->post('date');
        $meetings = array();

        if ($student && $date) {
            $class_meetings = $this->video_conference_model->getByClass($student->class_id, $student->section_id);
            
            foreach ($class_meetings as $meeting) {
                $meeting_date = date('Y-m-d', strtotime($meeting->start_time));
                if ($meeting_date == $date) {
                    $meetings[] = array(
                        'id' => $meeting->id,
                        'title' => $meeting->title,
                        'start_time' => date('H:i', strtotime($meeting->start_time)),
                        'end_time' => date('H:i', strtotime($meeting->end_time)),
                        'platform' => $meeting->platform,
                        'status' => $meeting->status,
                        'subject_name' => $meeting->subject_name,
                        'host_name' => $meeting->host_name . ' ' . $meeting->host_surname
                    );
                }
            }
        }

        echo json_encode($meetings);
    }
}
