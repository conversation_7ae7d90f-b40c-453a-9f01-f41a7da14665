<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['date_year'] = 'Metai';
$lang['date_years'] = 'Metai';
$lang['date_month'] = 'Mėnuo';
$lang['date_months'] = 'Mėnesiai';
$lang['date_week'] = 'Savaitė';
$lang['date_weeks'] = 'Savaitės';
$lang['date_day'] = 'Diena';
$lang['date_days'] = 'Dienos';
$lang['date_hour'] = 'Valanda';
$lang['date_hours'] = 'Valandos';
$lang['date_minute'] = 'Minutė';
$lang['date_minutes'] = 'Minutės';
$lang['date_second'] = 'Sekundė';
$lang['date_seconds'] = 'Sekundės';

$lang['UM12']	= '(UTC -12:00) Beikerio/Haulendo salos laikas';
$lang['UM11']	= '(UTC -11:00) Niujė laikas';
$lang['UM10']	= '(UTC -10:00) Havajų/Aleutų standartinis laikas, Kuko salų, Taičio laikas';
$lang['UM95']	= '(UTC -9:30) Markizo salų laikas';
$lang['UM9']	= '(UTC -9:00) Aliaskos standartinis laikas, Gambier salos laikas';
$lang['UM8']	= '(UTC -8:00) Ramiojo vandenyno standartinis laikas, Klipertono salos laikas';
$lang['UM7']	= '(UTC -7:00) Kalnų standartinis laikas';
$lang['UM6']	= '(UTC -6:00) Centro standartinis laikas';
$lang['UM5']	= '(UTC -5:00) Rytų standartinis laikas, Vakarų Karibų standartinis laikas';
$lang['UM45']	= '(UTC -4:30) Venesuelos standartinis laikas';
$lang['UM4']	= '(UTC -4:00) Atlanto standartinis laikas, Rytų karibų standartinis laikas';
$lang['UM35']	= '(UTC -3:30) Niufaundlendo standartinis laikas';
$lang['UM3']	= '(UTC -3:00) Argentinos, Brazilijos, Prancūzijos Gvianos, Urugvajaus laikas';
$lang['UM2']	= '(UTC -2:00) Pietų Džordžijos ir Pietų Sandvičo salų laikas';
$lang['UM1']	= '(UTC -1:00) Azorų, Žaliojo Kyšulio salų laikas';
$lang['UTC']	= '(UTC) Grynvičo laikas, Vakarų Europos laikas';
$lang['UP1']	= '(UTC +1:00) Centrinės Europos laikas, Vakarų afrikos laikas';
$lang['UP2']	= '(UTC +2:00) Centrinės Afrikos laikas, Rytų Europos laikas, Kaliningrado laikas';
$lang['UP3']	= '(UTC +3:00) Maskvos laikas, Rytų Afrikos laikas, Arabijos standartinis laikas';
$lang['UP35']	= '(UTC +3:30) Irano standartinis laikas';
$lang['UP4']	= '(UTC +4:00) Azerbaidžano standartinis laikas, Samaros laikas';
$lang['UP45']	= '(UTC +4:30) Afghanistano laikas';
$lang['UP5']	= '(UTC +5:00) Pakistano standartinis laikas, Jakaterinburgo laikas';
$lang['UP55']	= '(UTC +5:30) Indijos standartinis laikas, Šri Lankos laikas';
$lang['UP575']	= '(UTC +5:45) Nepalo laikas';
$lang['UP6']	= '(UTC +6:00) Bangladešo standartinis laikas, Butano laikas, Omsko laikas';
$lang['UP65']	= '(UTC +6:30) Cocos salos, Moianmaras';
$lang['UP7']	= '(UTC +7:00) Krasnojarsko laikas, Kambodžos, Laoso, Tailando, Vietnamo laikas';
$lang['UP8']	= '(UTC +8:00) Australijos Vakarų standartinis laikas, Pekino laikas, Irkutsko laikas';
$lang['UP875']	= '(UTC +8:45) Australijos Centro-Vakarų standartinis laikas';
$lang['UP9']	= '(UTC +9:00) Japonijos standartinis laikas, Korėjos standartinis laikas, Jakutsko laikas';
$lang['UP95']	= '(UTC +9:30) Australijos Centro standartinis laikas';
$lang['UP10']	= '(UTC +10:00) Australijos Rytų standartinis laikas, Vladivostoko laikas';
$lang['UP105']	= '(UTC +10:30) Lordo Hau salos laikas';
$lang['UP11']	= '(UTC +11:00) Srednekolymsko laikas, Saliamono salų laikas, Vanuatu laikas';
$lang['UP115']	= '(UTC +11:30) Norfolk Island';
$lang['UP12']	= '(UTC +12:00) Fidžio, Gilberto salų, Kamčiatkos laikas, Naujosios Zelandijos standartinis laikas';
$lang['UP1275']	= '(UTC +12:45) Čathamo salų standartinis laikas';
$lang['UP13']	= '(UTC +13:00) Samoa laiko zona, Fenikso salų laikas, Tongos laikas';
$lang['UP14']	= '(UTC +14:00) Linijinių salų laikas';
