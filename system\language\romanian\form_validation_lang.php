<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * <AUTHOR>
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= 'Câmpul {field} este obligatoriu.';
$lang['form_validation_isset']			= 'Câmpul {field} trebuie să conțină o valoare.';
$lang['form_validation_valid_email']		= 'Câmpul {field} trebuie să conțină o adresâ de email validă.';
$lang['form_validation_valid_emails']		= 'Câmpul {field} trebuie să conțină toate adresele de email valide.';
$lang['form_validation_valid_url']		= 'Câmpul {field} trebuie să conțină un URL valid.';
$lang['form_validation_valid_ip']		= 'Câmpul {field} trebuie să conțină un IP valid.';
$lang['form_validation_min_length']		= 'Câmpul {field} trebuie să fie de cel puțin {param} caractere lungime.';
$lang['form_validation_max_length']		= 'Câmpul {field} nu poate să depășească {param} caractere lungime.';
$lang['form_validation_exact_length']		= 'Câmpul {field} trebuie să fie de exact {param} caractere lungime.';
$lang['form_validation_alpha']			= 'Câmpul {field} poate conține doar caractere alfabetice.';
$lang['form_validation_alpha_numeric']		= 'Câmpul {field} poate să conțină doar caractere alfa-numerice.';
$lang['form_validation_alpha_numeric_spaces']	= 'Câmpul {field} poate să conțină doar caractere alfa-numerice și spații.';
$lang['form_validation_alpha_dash']		= 'Câmpul {field} poate să conțină doar caractere alfa-numerice, underscore-ur, și linii.';
$lang['form_validation_numeric']		= 'Câmpul {field} poate să conțină doar numere.';
$lang['form_validation_is_numeric']		= 'Câmpul {field} poate să conțină doar caractere numerice.';
$lang['form_validation_integer']		= 'Câmpul {field} poate să conțină doar un numâr integru.';
$lang['form_validation_regex_match']		= 'Câmpul {field} nu conține formatul corect.';
$lang['form_validation_matches']		= 'Câmpul {field} nu corespunde cu câmpul {param}.';
$lang['form_validation_differs']		= 'Câmpul {field} trebuie să difere de câmpul {param}.';
$lang['form_validation_is_unique'] 		= 'Câmpul {field} trebuie să conțină o valoare unică.';
$lang['form_validation_is_natural']		= 'Câmpul {field} trebuie să conțină doar numere.';
$lang['form_validation_is_natural_no_zero']	= 'Câmpul {field} trebuie să conțină doar numere și trebuie să fie mai mare decât 0.';
$lang['form_validation_decimal']		= 'Câmpul {field} trebuie să conțină un număr decimal.';
$lang['form_validation_less_than']		= 'Câmpul {field} trebuie să conțină un număr mai mic decât {param}.';
$lang['form_validation_less_than_equal_to']	= 'Câmpul {field} trebuie să conțină un număr mai mic sau egacl cu {param}.';
$lang['form_validation_greater_than']		= 'Câmpul {field} trebuie să conțină un număr mai mare decât {param}.';
$lang['form_validation_greater_than_equal_to']	= 'Câmpul {field} trebuie să conțină un număr mai mare decât sau egal cu {param}.';
$lang['form_validation_error_message_not_set']	= 'Nu se poate accesa un mesaj de eroare corespunzător numele tău domeniu {field}.';
$lang['form_validation_in_list']		= 'Câmpul {field} trebuie să fie unul dintre: {param}.';
