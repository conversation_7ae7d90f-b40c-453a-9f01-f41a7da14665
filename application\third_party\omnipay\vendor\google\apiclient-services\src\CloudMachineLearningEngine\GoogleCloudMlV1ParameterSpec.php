<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudMachineLearningEngine;

class GoogleCloudMlV1ParameterSpec extends \Google\Collection
{
  protected $collection_key = 'discreteValues';
  /**
   * @var string[]
   */
  public $categoricalValues;
  public $discreteValues;
  public $maxValue;
  public $minValue;
  /**
   * @var string
   */
  public $parameterName;
  /**
   * @var string
   */
  public $scaleType;
  /**
   * @var string
   */
  public $type;

  /**
   * @param string[]
   */
  public function setCategoricalValues($categoricalValues)
  {
    $this->categoricalValues = $categoricalValues;
  }
  /**
   * @return string[]
   */
  public function getCategoricalValues()
  {
    return $this->categoricalValues;
  }
  public function setDiscreteValues($discreteValues)
  {
    $this->discreteValues = $discreteValues;
  }
  public function getDiscreteValues()
  {
    return $this->discreteValues;
  }
  public function setMaxValue($maxValue)
  {
    $this->maxValue = $maxValue;
  }
  public function getMaxValue()
  {
    return $this->maxValue;
  }
  public function setMinValue($minValue)
  {
    $this->minValue = $minValue;
  }
  public function getMinValue()
  {
    return $this->minValue;
  }
  /**
   * @param string
   */
  public function setParameterName($parameterName)
  {
    $this->parameterName = $parameterName;
  }
  /**
   * @return string
   */
  public function getParameterName()
  {
    return $this->parameterName;
  }
  /**
   * @param string
   */
  public function setScaleType($scaleType)
  {
    $this->scaleType = $scaleType;
  }
  /**
   * @return string
   */
  public function getScaleType()
  {
    return $this->scaleType;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudMlV1ParameterSpec::class, 'Google_Service_CloudMachineLearningEngine_GoogleCloudMlV1ParameterSpec');
