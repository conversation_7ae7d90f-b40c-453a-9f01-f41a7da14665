<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['imglib_source_image_required'] = '設定で元画像を指定する必要があります。';
$lang['imglib_gd_required'] = 'この機能には、GD ライブラリが必要です。';
$lang['imglib_gd_required_for_props'] = '画像プロパティを取得するには、お使いのサーバで GD ライブラリがサポートされている必要があります。';
$lang['imglib_unsupported_imagecreate'] = 'この形式の画像を処理するためのGDの機能が、お使いのサーバでサポートされていません。';
$lang['imglib_gif_not_supported'] = 'GIF 形式の画像はライセンスの関係でサポートされていないことがよくあります。かわわりに JPG または PNG 画像を使用してください。';
$lang['imglib_jpg_not_supported'] = 'JPG 形式の画像はサポートされていません。';
$lang['imglib_png_not_supported'] = 'PNG 形式の画像はサポートされていません。';
$lang['imglib_jpg_or_png_required'] = '設定で指定されているい画像リサイズ方法は、JPEG または PNG 形式でのみ動作します。';
$lang['imglib_copy_error'] = 'ファイルを置き換えているときにエラーが発生しました。ファイルディレクトリが書き込み可能かどうか確かめてください。';
$lang['imglib_rotate_unsupported'] = '画像の回転はお使いのサーバでサポートされていない可能性があります。';
$lang['imglib_libpath_invalid'] = '画像ライブラリのパスが間違っています。設定で正しいパスをしてしてください。';
$lang['imglib_image_process_failed'] = '画像処理が失敗しました。サーバで指定した処理がサポートされているか、また画像ライブラリのパスが正しいかを確認してください。';
$lang['imglib_rotation_angle_required'] = '画像を回転するには、回転する角度を指定する必要があります。';
$lang['imglib_invalid_path'] = '画像のパスが間違っています。';
$lang['imglib_invalid_image'] = '提供された画像は無効です。';
$lang['imglib_copy_failed'] = '画像のコピーに失敗しました。';
$lang['imglib_missing_font'] = '使用するフォントが見つかりません。';
$lang['imglib_save_failed'] = '画像を保存できません。画像と保存先のディレクトリを書き込み可能にしてください。';
