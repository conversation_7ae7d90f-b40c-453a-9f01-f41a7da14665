<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']		= '{field}欄は必須フィールドです';
$lang['form_validation_isset']			= '{field}欄は値がなければいけません';
$lang['form_validation_valid_email']		= '{field}欄はメールアドレスとして正しい形式でなければいけません';
$lang['form_validation_valid_emails']		= '{field}欄は正しいメールアドレスでなければいけません';
$lang['form_validation_valid_url']		= '{field}欄は正しいURLでなければいけません';
$lang['form_validation_valid_ip']		= '{field}欄は正しいIPアドレスだければいけません';
$lang['form_validation_min_length']		= '{field}欄は{param}文字以上、でなければいけません';
$lang['form_validation_max_length']		= '{field}欄は{param}文字以下、でなければいけません';
$lang['form_validation_exact_length']		= '{field}欄は{param}文字でなければいけません';
$lang['form_validation_alpha']	 = '{field}欄は英字だけです';
$lang['form_validation_alpha_numeric']	= '{field}欄は英数字のみです';
$lang['form_validation_alpha_numeric_spaces']	= '{field}欄は英数字かスペースのみです';
$lang['form_validation_alpha_dash']		= '{field}欄は英数字、_(アンダースコア)、もしくは-(ハイフン)のみです';
$lang['form_validation_numeric']		= '{field}欄は数字のみです';
$lang['form_validation_is_numeric']		= '{field}欄は数字のみです';
$lang['form_validation_integer']		= '{field}欄は整数のみです';
$lang['form_validation_regex_match']		= '{field}欄は正しい形式ではありません';
$lang['form_validation_matches']		= '{field}欄が {param}欄と同じではありません';
$lang['form_validation_differs']		= '{field}欄は{param}欄と同じではいけません';
$lang['form_validation_is_unique'] 		= '{field}欄はユニークな値でなければなりません';
$lang['form_validation_is_natural']		= '{field}欄は数値だけです。';
$lang['form_validation_is_natural_no_zero']	= '{field}欄はゼロより大きい数値のみです。';
$lang['form_validation_decimal']		= '{field}欄は10進数のみです。';
$lang['form_validation_less_than']		= '{field}欄は{param}より小さい値でなければいけません。';
$lang['form_validation_less_than_equal_to']	= '{field}欄は{param}以下の値でなければいけません。';
$lang['form_validation_greater_than']		= '{field}欄は{param}より大きい値でなければいけません。';
$lang['form_validation_greater_than_equal_to']	= '{field}欄は{param}以上の値でなければいけません。';
$lang['form_validation_error_message_not_set']	= '{field}欄に適切なエラーメッセージがありません';
$lang['form_validation_in_list']		= '{field}欄は{param}のいずれかでなければなりません';
