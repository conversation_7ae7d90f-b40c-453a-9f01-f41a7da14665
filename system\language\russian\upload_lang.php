<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = "Невозможно найти переменную с именем userfile в массиве POST.";
$lang['upload_file_exceeds_limit'] = "Размер загружаемого файла превышает максимально допустимый размер, указанный в файле конфигурации PHP.";
$lang['upload_file_exceeds_form_limit'] = "Размер загружаемого файла превышает максимально допустимый размер, указанный в отправленной форме.";
$lang['upload_file_partial'] = "Загружаемый файл был получен только частично.";
$lang['upload_no_temp_directory'] = "Директория для хранения временных файлов не существует.";
$lang['upload_unable_to_write_file'] = "Не удалось записать файл на диск.";
$lang['upload_stopped_by_extension'] = "Расширение PHP остановило загрузку файла.";
$lang['upload_no_file_selected'] = "Необходимо выбрать файл для загрузки.";
$lang['upload_invalid_filetype'] = "Загрузка файлов данного типа запрещена.";
$lang['upload_invalid_filesize'] = "Размер загружаемого файла превышает максимально допустимый.";
$lang['upload_invalid_dimensions'] = "Размеры загружаемого изображения превышают максимально допустимые.";
$lang['upload_destination_error'] = "Невозможно перенести загруженный файл в директорию назначения.";
$lang['upload_no_filepath'] = "Некорректная директория для загрузки.";
$lang['upload_no_file_types'] = "Необходимо указать допустимые для загрузки типы файлов.";
$lang['upload_bad_filename'] = "Файл с указанным именем уже существует на сервере.";
$lang['upload_not_writable'] = "Директория для загрузки недоступна для записи.";
