<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014 - 2017, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ftp_no_connection'] = '無法找到一個有效的連線 ID。在執行任何檔案傳輸時請確認已經連線成功';
$lang['ftp_unable_to_connect'] = '無法使用提供的主機名稱連接到 FTP 伺服器';
$lang['ftp_unable_to_login'] = '無法登入 FTP 伺服器，請檢查使用者名稱及密碼是否正確';
$lang['ftp_unable_to_mkdir'] = '無法建立指定的資料夾';
$lang['ftp_unable_to_changedir'] = '無法切換目錄';
$lang['ftp_unable_to_chmod'] = '無法設定檔案權限，請確認檢查檔案路徑或使用者權限';
$lang['ftp_unable_to_upload'] = '無法上傳指定的檔案，檢查上傳路徑或使用者權限';
$lang['ftp_unable_to_download'] = '無法下載指定的檔案，檢查下載路徑或使用者權限';
$lang['ftp_no_source_file'] = '無法找到指定的原始檔案，請檢查檔案路徑或使用者權限';
$lang['ftp_unable_to_rename'] = '無法重新命名';
$lang['ftp_unable_to_delete'] = '無法刪除檔案';
$lang['ftp_unable_to_move'] = '無法移動檔案，請檢查目標資料夾是否存在';
