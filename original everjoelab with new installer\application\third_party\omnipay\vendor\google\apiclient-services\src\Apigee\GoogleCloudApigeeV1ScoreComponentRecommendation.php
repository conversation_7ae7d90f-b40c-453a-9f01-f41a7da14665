<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Apigee;

class GoogleCloudApigeeV1ScoreComponentRecommendation extends \Google\Collection
{
  protected $collection_key = 'actions';
  protected $actionsType = GoogleCloudApigeeV1ScoreComponentRecommendationAction::class;
  protected $actionsDataType = 'array';
  /**
   * @var string
   */
  public $description;
  /**
   * @var int
   */
  public $impact;
  /**
   * @var string
   */
  public $title;

  /**
   * @param GoogleCloudApigeeV1ScoreComponentRecommendationAction[]
   */
  public function setActions($actions)
  {
    $this->actions = $actions;
  }
  /**
   * @return GoogleCloudApigeeV1ScoreComponentRecommendationAction[]
   */
  public function getActions()
  {
    return $this->actions;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param int
   */
  public function setImpact($impact)
  {
    $this->impact = $impact;
  }
  /**
   * @return int
   */
  public function getImpact()
  {
    return $this->impact;
  }
  /**
   * @param string
   */
  public function setTitle($title)
  {
    $this->title = $title;
  }
  /**
   * @return string
   */
  public function getTitle()
  {
    return $this->title;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudApigeeV1ScoreComponentRecommendation::class, 'Google_Service_Apigee_GoogleCloudApigeeV1ScoreComponentRecommendation');
